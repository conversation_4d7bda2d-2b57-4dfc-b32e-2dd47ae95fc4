<PERSON><PERSON> Matt NRT SCNDR1: Regarding our integration test, so I spoke with <PERSON><PERSON> call yesterday and it and <PERSON> as well as <PERSON><PERSON><PERSON> and what we want to discuss today is an autumn away of doing a daily consistency check.
<PERSON><PERSON> Matt NRT SCNDR1: It's too late now to go back and look at the the DMOL file and and the raw data because that information has already been handed over to the DMF and the DMF will do the migration and if I understood correctly yeah it would either be Friday or Monday.
<PERSON><PERSON> Matt NRT SCNDR1: I don't recall what day it was, but umm.
<PERSON><PERSON> Matt NRT SCNDR1: Ideally what we want to do is after that information has been transported to ZT1, we will take an extract of the tables and.
<PERSON><PERSON> Matt NRT SCNDR1: From those tables we will set some some rules regarding the material master data in in for each tab that we are responsible for within domain.
<PERSON>more Matt NRT SCNDR1: That's meaning purchasing the MRP views in any additional tips, but what is vital to this is to know what rule we should we should have.
<PERSON>more Matt NRT SCNDR1: Yeah.
<PERSON>more Matt NRT SCNDR1: So over the course of today, we will discuss some of the those things we need the input of everyone as everyone has vital information.
<PERSON><PERSON> Matt NRT SCNDR1: But what we want the outcome of this practice will be that we don't have are we have.
<PERSON><PERSON> Matt NRT SCNDR1: From a data perspective, a seamless UAT testing and 5th and fowlerville and ECHO.
<PERSON><PERSON> Matt NRT SCNDR1: Is that OK for everyone that we utilize this meeting today for that?
Segura <PERSON> Angel MNR SCNDR1: Yes.
Ochoa Laura MNR UELP: Yes, it's OK.
Ashmore Matt NRT SCNDR1: Yeah.
<PERSON>more Matt NRT SCNDR1: So again, I will be so it's it's tough timing, but I will be going on vacation soon.
Ashmore <PERSON> NRT SCNDR1: Jan will be leading this activity for me in in my absence, but he will have that you guys providing the information and Tiago will as a as he's not allocated to a project will be responsible for taking the data create implementing the rules and creating an automated solution to basically tell us, hey, what are the issues and the master data and the product master data that need to be corrected and we will either create bugs, your tickets for this for for data issues.
Ashmore Matt NRT SCNDR1: But I spoke with the installer or the earlier today.
Ashmore Matt NRT SCNDR1: Nevertheless, whatever we identified, he will support us in ensuring that it's corrected before our sanity tech check starting July 29th, our 30th.
Ashmore Matt NRT SCNDR1: I also wanted to show you guys one other thing.
Ashmore Matt NRT SCNDR1: Umm.
Ashmore Matt NRT SCNDR1: And I will share my screen once second.
Ashmore Matt NRT SCNDR1: We we were talking about, let's call it a.
Ashmore Matt NRT SCNDR1: How to say what this is?
Ashmore Matt NRT SCNDR1: At least it was called a material master data matrix.
Ashmore Matt NRT SCNDR1: Basically a starting point for.
Ashmore Matt NRT SCNDR1: OHS to use as a reference on the fields in what should be what should be in those fields for each different material type, and this has been started by.
Ashmore Matt NRT SCNDR1: Philip Heitz.
Ashmore Matt NRT SCNDR1: And I think it's it doesn't contain every field, but it does contain a lot of information and I think it's at least a, a a baseline for us to start working with in regards to the rules and what needs to be maintained for each part.
Ashmore Matt NRT SCNDR1: Is everyone?
Ashmore Matt NRT SCNDR1: I mean, this is kind of what we were asking for, right?
Ashmore Matt NRT SCNDR1: In the previous calls.
Okal Jan GNL SCNDR1: Guess yes.
Ashmore Matt NRT SCNDR1: I feel like I'm alone here but.
Ashmore Matt NRT SCNDR1: But.
Okal Jan GNL SCNDR1: We are speechless.
Ashmore Matt NRT SCNDR1: Yeah, me too sometimes.
Ashmore Matt NRT SCNDR1: But.
Ashmore Matt NRT SCNDR1: I.
Ashmore Matt NRT SCNDR1: I don't have all the answers, but I think this is the way forward.
Ashmore Matt NRT SCNDR1: We need to do this data consistency check to ensure that we, at least from domain S perspective, have a all of our data either correct or completed.
Ashmore Matt NRT SCNDR1: Go ahead, yeah.
Okal Jan GNL SCNDR1: I I have one one very first question is here for Phantom Backflush indicator market is blank as far as I know from LP one, this is incorrect.
Okal Jan GNL SCNDR1: This is a big error, so I don't know unless very first, you know glance and this one I have already doubts.
Ashmore Matt NRT SCNDR1: Yeah, for sure it for sure to working progress.
Ashmore Matt NRT SCNDR1: I I have doubts as well, but we we need to to come.
Ashmore Matt NRT SCNDR1: Yeah, I I'm not looking.
Ashmore Matt NRT SCNDR1: I'm.
Ashmore Matt NRT SCNDR1: I'm saying it may not be anymore.
Ashmore Matt NRT SCNDR1: I hate to say that like this, but it may not be 100% correct for UAT, but at least it's enough to get us through UAT and then there will be a phase two to ensure that it's it's close to 100% correct before we go into production so.
Ashmore Matt NRT SCNDR1: I need everyone.
Ashmore Matt NRT SCNDR1: I mean everyone's input here on how we should proceed and and what is the best way to proceed.
Ashmore Matt NRT SCNDR1: Any ideas?
Stevaux Jorge SRC SCNDR1: Creating queries Matt we created for SD side actually Edmar created but that was helpful for the SD to look to some points you know.
Stevaux Jorge SRC SCNDR1: Yes, QV I that was the transaction.
Stevaux Jorge SRC SCNDR1: You didn't have everything.
Stevaux Jorge SRC SCNDR1: We don't.
Stevaux Jorge SRC SCNDR1: I don't know everything that needs to be maintained.
Stevaux Jorge SRC SCNDR1: Honestly, it's.
Stevaux Jorge SRC SCNDR1: But anyway, through a.
Stevaux Jorge SRC SCNDR1: Through a query we are able to validate many many, many of the things.
Stevaux Jorge SRC SCNDR1: If we know what we needed to check.
Stevaux Jorge SRC SCNDR1: Yeah, like you can.
Giron Jorge MNR SCNDR1: I would like go for his comment because that is very useful and honestly I don't.
Giron Jorge MNR SCNDR1: I don't have the knowledge also as well, because for example in starting I I need to download a lot of tables in order and then paste them or having a big lookup and it takes so much time, so I will echo Jorge his comment to to get that.
Ashmore Matt NRT SCNDR1: Yeah, but but, but guys, you won't be so.
Ashmore Matt NRT SCNDR1: So again, as I said, Tiago, as he's not assigned to any project, we need to give him the rules.
Ashmore Matt NRT SCNDR1: He will execute all of this for us, so I'm not asking you guys to pull the tables, check the the data I'm saying provide the rules, provide the inputs is if you're current type, is always easy for finished goods, then that's one rule and then and then Tiago will create some automated solution to do all of this for us.
Ashmore Matt NRT SCNDR1: Umm, you guys are extremely busy and don't have the capacity to do that.
Ashmore Matt NRT SCNDR1: I'm not asking you to do that, but what I am asking is you to provide the rules.
Segura Jose Angel MNR SCNDR1: Yeah, just a.
Ashmore Matt NRT SCNDR1: Yeah, but, but if a query is, is is an option for SD George I I'm I'm OK with it.
Stevaux Jorge SRC SCNDR1: What? You.
Stevaux Jorge SRC SCNDR1: Yeah, you're looking for with Tiago.
Stevaux Jorge SRC SCNDR1: Yeah, maybe the solution would be even better.
Stevaux Jorge SRC SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: But but what I can't do is is Sean Chung had such a high success rate because.
Ashmore Matt NRT SCNDR1: They started a new process.
Ashmore Matt NRT SCNDR1: Well, basically they, they umm.
Ashmore Matt NRT SCNDR1: Replace the DMOL actually with an Excel spreadsheet and it's like a PFEP a plan for every part and they went through every field and maintained it manually.
Ashmore Matt NRT SCNDR1: Months and months ago.
Ashmore Matt NRT SCNDR1: So now they're data is more or less 100% correct.
Ashmore Matt NRT SCNDR1: I know that the the colleagues in Asia Pacific are pushing some changes in the future for future rollouts regarding the D mall and and data accuracy in.
Ashmore Matt NRT SCNDR1: In you know what is the input, but we can't.
Ashmore Matt NRT SCNDR1: We don't, we don't have the luxury of waiting until then.
Ashmore Matt NRT SCNDR1: Otherwise, to be honest guys, we will have another.
Ashmore Matt NRT SCNDR1: Slow and painful.
Ashmore Matt NRT SCNDR1: Test with the key users.
Ashmore Matt NRT SCNDR1: Silence.
Segura Jose Angel MNR SCNDR1: Yeah, correct.
Stevaux Jorge SRC SCNDR1: Yeah, alright.
Stevaux Jorge SRC SCNDR1: Yeah, just guessing where do we start? No.
Giron Jorge MNR SCNDR1: That as well in probably we can start with the demos.
Giron Jorge MNR SCNDR1: You can take the table from there and we can have the mandatory fields highlighted.
Giron Jorge MNR SCNDR1: I don't know.
Giron Jorge MNR SCNDR1: I'm just thinking out loud.
Kota Christina AHL SCNDR1: You have to be careful with that though, because in the demos like I I mentioned a couple Times Now and I couple different meetings, I don't believe a deep analysis was done of those demo files and because of that there is a lot of mist opportunity to identify different configurations behind different fields, even if they match fields that are missing from the demo file.
Kota Christina AHL SCNDR1: Because we were really just focused on providing the information that was already there rather than analyzing it and making sure that every field that we needed was there.
Kota Christina AHL SCNDR1: And I'm not sure we even knew of all the fields that we needed.
Kota Christina AHL SCNDR1: I I mean it especially for my case.
Kota Christina AHL SCNDR1: I'm not so familiar with EWM, so I wasn't very familiar with, you know, inside the production version there needed to be some extra fields feel filled out that I wasn't aware of.
Kota Christina AHL SCNDR1: So I think that you'd have to just be really cautious with just, you know, taking the rules from the demo file and and applying it.
Kota Christina AHL SCNDR1: I mean, I think you would need to analyze and umm, you know, deep dive the demo file and and make sure that in your area it's correct and that all the fields are accounted for.
Stevaux Jorge SRC SCNDR1: Then we go on the other way around.
Stevaux Jorge SRC SCNDR1: Instead of using demo as the template, maybe the model, yes, but we go from SAP.
Stevaux Jorge SRC SCNDR1: From what we know in SAP, maybe from tables from what we know from master data, usually master data or BP is right either vendor or customer.
Giron Jorge MNR SCNDR1: And the materials?
Okal Jan GNL SCNDR1: I I would proposal was if they really do upload the data by Friday or by Monday, we could download the the whole table, download the whole mark table.
Okal Jan GNL SCNDR1: You shall have all the fields here and then we can have a exported in Excel and do some.
Okal Jan GNL SCNDR1: You know checking there.
Araujo Thiago GCR SCNDR1: Yeah, I think this is what Matt wants me to do, right, Matt?
Araujo Thiago GCR SCNDR1: But I the biggest effort I think is to figure out which fields what.
Araujo Thiago GCR SCNDR1: What is the standard?
Araujo Thiago GCR SCNDR1: Let's say for finished goods and finish and for Roma material.
Araujo Thiago GCR SCNDR1: What's the standard?
Araujo Thiago GCR SCNDR1: Because then the cross check, I think this is what Matt wants me to do.
Araujo Thiago GCR SCNDR1: Download the the the tables and do this cross check and create maybe something that make it easier for all the other ones that we need in the future that we can just provide it if possible.
Araujo Thiago GCR SCNDR1: Yeah, that's why also, I book a meeting tomorrow that we can see the template for M17 right to upload because then I can maybe extract the data after the cross check.
Araujo Thiago GCR SCNDR1: What's wrong?
Araujo Thiago GCR SCNDR1: What's right?
Araujo Thiago GCR SCNDR1: I can already doing the right template just to upload in the SAP and do the correction.
Ashmore Matt NRT SCNDR1: And and and it may be that we don't use M17, it may be that we do use M17, but what we what we will do is for sure to try to to use the DMF to make the corrections.
Ashmore Matt NRT SCNDR1: Of course they will have to.
Ashmore Matt NRT SCNDR1: We will have to make some recording in JIRA so that way it's it's corrected for the final cutover.
Ashmore Matt NRT SCNDR1: But yeah, the output should be in some way that it's usable for us.
Kota Christina AHL SCNDR1: I think another thing too like output condition records.
Kota Christina AHL SCNDR1: That's not something that would be included in a demo file, or necessarily a data transfer, but it's something absolutely mandatory to print anything, so you know, just keeping in mind, like, how do we capture those requirements?
Kota Christina AHL SCNDR1: Also, those additional requirements that are needed.
Ashmore Matt NRT SCNDR1: And and for sure we won't catch everything.
Ashmore Matt NRT SCNDR1: And Christina, that's a good point.
Ashmore Matt NRT SCNDR1: We need to think about how we can ensure that we have the output condition records maintained properly, but if we just start with product master and material master, I think that that takes care of a lot of the issues, at least that I know we had in Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: Any input?
Giron Jorge MNR SCNDR1: I will suggest that we start with the material must.
Kota Christina AHL SCNDR1: Yeah, I agree.
Kota Christina AHL SCNDR1: It's starting with.
Kota Christina AHL SCNDR1: Those are a good idea, but I think after after those we need to think about the other things that are also.
Kota Christina AHL SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: Because, you know in in not only that I've I've put down some things.
Ashmore Matt NRT SCNDR1: So when I'm back from vacation, Jose and I can discuss for Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: You know, supply areas in in comma control cycles and replenishment strategies and all those things we need to discuss prior to to sanity check.
Ashmore Matt NRT SCNDR1: But I just want to start with something.
Ashmore Matt NRT SCNDR1: I don't want to say that we were, yeah, complacent and and and did nothing to to improve the situation.
Ashmore Matt NRT SCNDR1: That's why I'm suggesting this and asking for input.
Giron Jorge MNR SCNDR1: Eight months.
Giron Jorge MNR SCNDR1: I'm just curious.
Giron Jorge MNR SCNDR1: We are a very big global company in uh, like I've been said to you.
Giron Jorge MNR SCNDR1: Is it?
Giron Jorge MNR SCNDR1: They are doing fine.
Giron Jorge MNR SCNDR1: Should we just see their data and and you know?
Ashmore Matt NRT SCNDR1: I unfortunately we discussed that option with the Chinese colleagues this morning and and or the colleagues from Asia Pacific and Ian in because they are doing they they first of all the the the extractions have already taken place and the upload is in let's say preparation and progress.
Ashmore Matt NRT SCNDR1: I'm what China did won't help us now.
Stevaux Jorge SRC SCNDR1: Found that that.
Ashmore Matt NRT SCNDR1: It may help us in the future for a new projects, but it won't help us for the existing running projects because they changed the whole way of migrating the data.
Ashmore Matt NRT SCNDR1: With the DMF.
Stevaux Jorge SRC SCNDR1: Umm.
Okal Jan GNL SCNDR1: So they they took a risk.
Okal Jan GNL SCNDR1: They went outside of the system and they were successful in this case.
Okal Jan GNL SCNDR1: So and we are the stupid one.
Kota Christina AHL SCNDR1: And whoever was in charge of that should be in charge of the DMF team.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: So.
Ashmore Matt NRT SCNDR1: So actually my yeah, I don't know how they will take it.
Ashmore Matt NRT SCNDR1: They there are some suggestions going on and some discussions and further discussions will take place.
Ashmore Matt NRT SCNDR1: How to move forward with it?
Ashmore Matt NRT SCNDR1: But but I agree with you, what they did was remarkable, but it took a lot of time because it was a manual effort and somehow they have to automate that in the future as well, but.
Ashmore Matt NRT SCNDR1: We need to see.
Ashmore Matt NRT SCNDR1: Yeah, DMF should take some accountability and responsibility there, but but not to really focus on that.
Ashmore Matt NRT SCNDR1: I I just want to know from you guys.
Ashmore Matt NRT SCNDR1: Is this?
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: In in Jan you mentioned about the backflush.
Ashmore Matt NRT SCNDR1: Of course, this is not 100%, but Asia Pacific.
Ashmore Matt NRT SCNDR1: Actually this is from them and Philip Heitz and they use this as many of their rules, meaning of their rules.
Ashmore Matt NRT SCNDR1: All of these different fields now I'm only concerned and they did it for not only the main S, but they did it for all domains, so they got input from everyone that was a cross functional activity.
Ashmore Matt NRT SCNDR1: But I'm really focused on, at least for us domain S so.
Ashmore Matt NRT SCNDR1: So can we?
Ashmore Matt NRT SCNDR1: Yeah, use this in in your guys's opinion as a a baseline.
Ashmore Matt NRT SCNDR1: Of course, these are the field names on the technical field names, but the field names and.
Ashmore Matt NRT SCNDR1: If it needs to be amended, Jan as you said, because of of of some issues you already seen.
Okal Jan GNL SCNDR1: I just don't need to clarify with Philip, cause who knows what the customizing is behind some of these settings.
Ashmore Matt NRT SCNDR1: And and Philip is, I mean you spoke directly with Philip and asking him, you know, how this.
Ashmore Matt NRT SCNDR1: Where?
Ashmore Matt NRT SCNDR1: When he foreseen having this finished, you of course touch base with Philip, but I don't think it will be before you 80 or actually I'm sure it will not be before you 80.
Ashmore Matt NRT SCNDR1: So if we need to, to and if I need to, I can download this.
Ashmore Matt NRT SCNDR1: And because it's it's in a.
Ashmore Matt NRT SCNDR1: Folder which in the SharePoint which many of you may not have access to.
Ashmore Matt NRT SCNDR1: I can download this, share it with everyone and.
Stevaux Jorge SRC SCNDR1: Thank you.
Ashmore Matt NRT SCNDR1: Make your or or.
Ashmore Matt NRT SCNDR1: I mean, maybe we should have a a common file, but make your adjustments here if it should be, we should add a a field as Christina said before and it's not present here.
Ashmore Matt NRT SCNDR1: Then we should add it and say what it should be for finished goods semi finished goods, fantom raw material and so on.
Ashmore Matt NRT SCNDR1: Are to someone have a different proposal?
Ashmore Matt NRT SCNDR1: We just started from scratch.
Okal Jan GNL SCNDR1: No, we don't have any time to start from scratch.
Okal Jan GNL SCNDR1: Wait, we should take this.
Okal Jan GNL SCNDR1: If we have doubts about anything, we can try to clarify it, or at least you know, put put it on a OK, we will review it next week or something that we can start going ahead.
Ashmore Matt NRT SCNDR1: And that's what I would like to encourage everyone to do is, is to go ahead with this.
Ashmore Matt NRT SCNDR1: Let's don't let's don't wait.
Ashmore Matt NRT SCNDR1: Umm, I'm not again, Tiago.
Ashmore Matt NRT SCNDR1: Well, do all the exports and and all these things, we just need to make sure us as a team provides him the rules and if this is how we provide them to him, my expectation for Tiago is to take these and put it in a workable fashion for him to compare it against the market table.
Giron Jorge MNR SCNDR1: Him I I have a a question taking advantage of the forum I how do they identify the raw material if we don't have a valuation class for that?
Ashmore Matt NRT SCNDR1: So when we do the export from from ZT1 the table directly we talked to John Listermann yesterday, the migration scope master from Fenton and Fowlerville and he told us with 90 or 95% of surety that the evaluation classes are correct.
Ashmore Matt NRT SCNDR1: And then we can determine if it's a finished good, semi finished good, phantom or or raw material.
Ashmore Matt NRT SCNDR1: In the export, this is what you meant, right? Alright.
Giron Jorge MNR SCNDR1: Yeah, just just to because I I know how to identify the rest because of the material type and then the valuation class, right?
Giron Jorge MNR SCNDR1: But I haven't seen a valuation class for the raw material.
Giron Jorge MNR SCNDR1: I know that the Phantom they are.
Giron Jorge MNR SCNDR1: How can I say identified with the special procurement type, but I was just curious about the raw material.
Ashmore Matt NRT SCNDR1: And the the raw material of Ohrid, yeah.
Ashmore Matt NRT SCNDR1: Mm-hmm.
Okal Jan GNL SCNDR1: It will be 1000 or or simply 1010 or 20.
Okal Jan GNL SCNDR1: And for the intercompany, I don't think we have anything at 10:20.
Ashmore Matt NRT SCNDR1: But based on the valuation clips, we will determine if it's finished good semi finished good or phantom role and so on.
Giron Jorge MNR SCNDR1: That's why I was asking me because we don't have like a specific for raw material that that was my like.
Giron Jorge MNR SCNDR1: My question so should be that that 1000.
Okal Jan GNL SCNDR1: Or the 1000, it's called direct material.
Okal Jan GNL SCNDR1: Third party?
Okal Jan GNL SCNDR1: Yeah, that's the roll.
Giron Jorge MNR SCNDR1: Just just wanted to have that clarification.
Ashmore Matt NRT SCNDR1: OK, so how go ahead.
Okal Jan GNL SCNDR1: Ohh and there might be some question if you have a data and for those cases OK direct material, direct material, intercompany or semi finished and semi finished subcontracted and stuff like that.
Okal Jan GNL SCNDR1: Ohh, that might not be absolutely clear.
Okal Jan GNL SCNDR1: Ohh which if part is where the part belongs that there we need to have an impact from the key users or for the from the Tais and listeners of the world.
Ashmore Matt NRT SCNDR1: On the subcontracting?
Ashmore Matt NRT SCNDR1: OK.
Okal Jan GNL SCNDR1: So those exception cases I, yeah, let's call it exception, yeah.
Ashmore Matt NRT SCNDR1: Mm-hmm.
Ashmore Matt NRT SCNDR1: So what I will do is I'm downloading this file now.
Ashmore Matt NRT SCNDR1: I can either in yarn.
Ashmore Matt NRT SCNDR1: Please tell me what you prefer but I can send it out to everyone.
Ashmore Matt NRT SCNDR1: Everyone have a look at it again.
Ashmore Matt NRT SCNDR1: Remember, this is a working document, so nothing is complete.
Ashmore Matt NRT SCNDR1: Philip is still doing it.
Ashmore Matt NRT SCNDR1: He will have the final version at some point in time, but.
Okal Jan GNL SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: I think it's at least a foundation to start on and if everyone can then take the time to look over this, what for Fridays E2E call, unfortunately or fortunately.
Ashmore Matt NRT SCNDR1: I will not be able to join as I have some interviews that I need to conduct, but we can.
Ashmore Matt NRT SCNDR1: Let's say not unless you on you disagree.
Ashmore Matt NRT SCNDR1: Umm, we will not officially have the meeting, but we can use this time really Friday to go through this.
Ashmore Matt NRT SCNDR1: Material master data matrix and fulfill it with whatever information.
Ashmore Matt NRT SCNDR1: So I won't cancel the meeting.
Ashmore Matt NRT SCNDR1: I will leave it there.
Ashmore Matt NRT SCNDR1: So you have a blocker in your calendar to really take a look at this.
Okal Jan GNL SCNDR1: Yeah. OK.
Okal Jan GNL SCNDR1: Wait, we will go through some of the things with the Tiago tomorrow.
Okal Jan GNL SCNDR1: We have a meeting.
Okal Jan GNL SCNDR1: We will discuss what we can get there where if.
Ashmore Matt NRT SCNDR1: Mm-hmm.
Okal Jan GNL SCNDR1: And then if my Friday.
Okal Jan GNL SCNDR1: So some of the colleagues here could have some of the at least initial or beginning of the rules like, hey, if we have a finished goods, it has to be.
Okal Jan GNL SCNDR1: Ohh.
Okal Jan GNL SCNDR1: You know, in-house produced.
Okal Jan GNL SCNDR1: Or similar, and further down the you know more complicated ones.
Okal Jan GNL SCNDR1: Then we could start working on those with Tiago.
Ashmore Matt NRT SCNDR1: Mm-hmm.
Ashmore Matt NRT SCNDR1: I'm gonna share.
Ashmore Matt NRT SCNDR1: I'm gonna put it in the chat though.
Ashmore Matt NRT SCNDR1: Hopefully it works like that.
Okal Jan GNL SCNDR1: And then one question or posible tool was that IP which is there but I I haven't worked with whatever is currently in the in the template, so I'm not sure if anybody else has a experience with that or not.
Okal Jan GNL SCNDR1: There's a lot of stuff in it which could identify something is missing or not.
Okal Jan GNL SCNDR1: The everybody work ever with the IP.
Ashmore Matt NRT SCNDR1: Is everyone know what he's referring to the IP cockpit?
Segura Jose Angel MNR SCNDR1: No, no, I do not.
Okal Jan GNL SCNDR1: If not, then I guess nobody I worked with it before in LP one, but I'm not sure if it's the same or how similar that is, but that's something we will look at with Tiago.
Okal Jan GNL SCNDR1: Enter it.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Yeah, I can pull it up if anyone wants to take a quick look and see if it brings back any memories.
Stevaux Jorge SRC SCNDR1: You know, or even shared the transaction code for with us maybe.
Okal Jan GNL SCNDR1: Yeah.
Okal Jan GNL SCNDR1: Ohh yeah.
Okal Jan GNL SCNDR1: OK, this is the file with the views OK.
Okal Jan GNL SCNDR1: We'll put in the IP as well.
Okal Jan GNL SCNDR1: Transaction there or?
Ashmore Matt NRT SCNDR1: Yeah, I'm doing it now.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And now share my screen.
Ashmore Matt NRT SCNDR1: This is for Fenton.
Ashmore Matt NRT SCNDR1: I put in plant 4606.
Ashmore Matt NRT SCNDR1: I think that's Fenton and and I'm in ZE1, so here you can see the like maybe it's kind of like the query he created a Jorge, but here you have a lot of different.
Stevaux Jorge SRC SCNDR1: Umm.
Ashmore Matt NRT SCNDR1: Information procurement type valuation class.
Okal Jan GNL SCNDR1: Yeah, the the one, the ones that look like a A bulb light bulb.
Okal Jan GNL SCNDR1: Yeah, some of the if it's yellow, it means it exists.
Okal Jan GNL SCNDR1: If it white, it doesn't exist, so it could be like hey, for this one, we do not have a a routing and it shall be a produced then it something bad.
Okal Jan GNL SCNDR1: We don't have it shall be purchased, but we don't have inforecord and stuff like that.
Okal Jan GNL SCNDR1: There is a lot of information here to very useful if you can.
Okal Jan GNL SCNDR1: If one can download it and start sorting for something, hey all my purchased material from intercompany, what do I need everything and we can check if it's exists and if it's correct the settings are there and if the views even exist and stuff like that or specific.
Okal Jan GNL SCNDR1: Areas quality, inforecord and stuff like that.
Ashmore Matt NRT SCNDR1: Hmm, so this this is another option you know to use.
Okal Jan GNL SCNDR1: Let's see here.
Okal Jan GNL SCNDR1: Those are the views that the green ones or those are the views in the material master.
Okal Jan GNL SCNDR1: So, as we shall have inventory and we don't have a warehouse view or storage view, that's not good or if somewhere we are missing accounting views and stuff like that.
Giron Jorge MNR SCNDR1: I couldn't access to that transaction that somebody else had the same issue or just me.
Giron Jorge MNR SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Sorry if you put it in the search bar, you must do it like this.
Okal Jan GNL SCNDR1: Or add it to favorites as a favorite transaction with whatever is written there, and that your work.
Ashmore Matt NRT SCNDR1: Mm-hmm.
Giron Jorge MNR SCNDR1: We got it.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: So again.
Ashmore Matt NRT SCNDR1: In the coming days, please use the time to to add rules or take a look at that spreadsheet and our material master matrix and fulfill requirements that you know of.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And then we will for sure combine those, put them in in one excel and then provide it to to Tiago in order to utilize that as our baseline for validating our material master views.
Ashmore Matt NRT SCNDR1: And again, the exercises is may take a few minutes from each of you guys, but overall Tiago will drive it from from his side.
Ashmore Matt NRT SCNDR1: I mean, well, the driver is young, but Thiago will will do the, let's say, labor.
Stevaux Jorge SRC SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Anyone else?
Ashmore Matt NRT SCNDR1: Abraham, I know you have a lot of experience with this.
Ashmore Matt NRT SCNDR1: Maybe there's some some ideas from your side.
Ashmore Matt NRT SCNDR1: Maybe if you were talking we don't hear you.
Ashmore Matt NRT SCNDR1: Renee, with your IT background, maybe you have some some ideas as well.
Cabrera Abraham EXT Robert Bosch Mexico: Hello can you?
Cabrera Abraham EXT Robert Bosch Mexico: Hello. Hello.
Cabrera Abraham EXT Robert Bosch Mexico: OK.
Cabrera Abraham EXT Robert Bosch Mexico: Yeah, it's very.
Cabrera Abraham EXT Robert Bosch Mexico: I I was saying think that this is a good start.
Cabrera Abraham EXT Robert Bosch Mexico: I this perfect for identify opportunities on the on the master data, but we need to.
Cabrera Abraham EXT Robert Bosch Mexico: To assure that we are going to have a kind of dry test.
Cabrera Abraham EXT Robert Bosch Mexico: Uh.
Cabrera Abraham EXT Robert Bosch Mexico: In order to avoid any any situation that we can find different to the master data.
Cabrera Abraham EXT Robert Bosch Mexico: So that's something that we need to do, remember.
Ashmore Matt NRT SCNDR1: And and yeah, Fernando and I, along with Roberto and in Jeanine, have been discussing this, and it, tentatively, tentatively, we have planned for a so called Senator check for both the NFL and ECHO.
Ashmore Matt NRT SCNDR1: Starting July 30th and ending on July 1st or second, depending upon how far we go.
Ashmore Matt NRT SCNDR1: I mean August 2nd.
Ashmore Matt NRT SCNDR1: Sorry, I apologize.
Ashmore Matt NRT SCNDR1: So we'll have it a week before we're on site in, in Fenton and Fowlerville and and Echo.
Cabrera Abraham EXT Robert Bosch Mexico: OK.
Cabrera Abraham EXT Robert Bosch Mexico: Yeah, sounds good.
Ashmore Matt NRT SCNDR1: Mm-hmm.
Ashmore Matt NRT SCNDR1: All right, guys.
Ashmore Matt NRT SCNDR1: You you're the quietest group of.
Segura Jose Angel MNR SCNDR1: We are thinking.
Ashmore Matt NRT SCNDR1: Uh. Yeah.
Ashmore Matt NRT SCNDR1: OK. Yeah.
Ashmore Matt NRT SCNDR1: So that's kind of what I had for today.
Ashmore Matt NRT SCNDR1: I mean, I won't.
Ashmore Matt NRT SCNDR1: I want to best utilize our time and of course this is actually meant for some training and in the coming days you guys I will leave it on the schedule for you guys to use at your discretion that time.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And if you want to continue with the the meeting, just to let everyone know that way you can discuss these kind of topics in this one hour.
Ashmore Matt NRT SCNDR1: Whatever you guys wanna utilize it for? Yeah.
Okal Jan GNL SCNDR1: Yeah, well, that would be umm, yeah.
Ashmore Matt NRT SCNDR1: But for everyone?
Ashmore Matt NRT SCNDR1: Yan will be my proxy or as the German colleagues call it, deputy during my vacation.
Ashmore Matt NRT SCNDR1: So if you guys need anything, please reach out to you on.
Stevaux Jorge SRC SCNDR1: OK, we're not limiting to mark, right?
Stevaux Jorge SRC SCNDR1: Whatever table we know that can impact you here, yeah.
Okal Jan GNL SCNDR1: Yes, yes, yes, that this was just an example for the material master settings.
Okal Jan GNL SCNDR1: So yeah, main one.
Okal Jan GNL SCNDR1: Yeah, but you will have for a purchasing or sales.
Stevaux Jorge SRC SCNDR1: And then of course, not only the fields, we need to know, but the content of the field as well, right?
Stevaux Jorge SRC SCNDR1: If we for this field in Brazil, we need one.
Stevaux Jorge SRC SCNDR1: If something like this it's it's like the whole demo translated into a different with the right results.
Okal Jan GNL SCNDR1: Because if the data is migrated, we could upload, you know download whatever we have this mark table, this table and this fields and check what is in there and if it's missing then first we could fix it with the M17.
Okal Jan GNL SCNDR1: 2nd we could create a ticket or add it to the demo files to make sure it's in there for the live go live migration.
Stevaux Jorge SRC SCNDR1: OK.
Ashmore Matt NRT SCNDR1: And George, you know a lot about the outbound side and what what tables and things are necessary.
Ashmore Matt NRT SCNDR1: Umm so your your input is valuable here.
Okal Jan GNL SCNDR1: And one more point to this, uh.
Okal Jan GNL SCNDR1: If you could look into the results from the integration test, because in many cases we already identified some of the fields which were missing or settings that were missing.
Okal Jan GNL SCNDR1: And to be honest, I doubt that they would end up being there for the next migration and many cases, so it could be the same repeating situation.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And it's ad miles were not updated.
Ashmore Matt NRT SCNDR1: For sure it won't be there.
Okal Jan GNL SCNDR1: Yeah.
Okal Jan GNL SCNDR1: We may have updated some of the mappings or the set that ohh this is not a blank field but it needs to be filled something.
Okal Jan GNL SCNDR1: But to be honest, I doubt that it has been implemented in our present, if at all.
Okal Jan GNL SCNDR1: Those would be the first case is to check because that's for sure what failed during the testing at the very beginning.
Okal Jan GNL SCNDR1: A promise I will review our F2F test.
Okal Jan GNL SCNDR1: So from your test, uh, try to check yours.
Okal Jan GNL SCNDR1: And for example for me I will try to share with Christina and Christina if you have some of the things which you are discovered as was failing shared with with me as well.
Stevaux Jorge SRC SCNDR1: And then we maybe if you Tiago gets repeated information, then he better than no information, right?
Stevaux Jorge SRC SCNDR1: So if I send something about 1 field, whoever found something for the same field that that's not a problem.
Okal Jan GNL SCNDR1: And in many cases we are we are not even talking about having a setting which is perfect for that specific business case.
Okal Jan GNL SCNDR1: But if something was completely missing, I don't care if the rounding value is 100 or one or ohh thousand.
Okal Jan GNL SCNDR1: But if something was missing completely and it failed us on that for in the testing, that's worse than if we just put one and it might be 10.
Okal Jan GNL SCNDR1: In my view.
Ashmore Matt NRT SCNDR1: And and that's the phase two that we discussed, Jan.
Ashmore Matt NRT SCNDR1: Yeah, phase two is after UAT, then we can discuss how we can make it, especially for Fenton and file because it will go live in October, more accurate it meaning if the rounding value was set to one and then it should be 100, OK, we discussed it then, but today's just about if if the field is required and necessary that it has something there, not that the rounding value is just not maintained.
Okal Jan GNL SCNDR1: Like for example the very first ones is the extension of materials to the storage locations.
Okal Jan GNL SCNDR1: None of that was done.
Okal Jan GNL SCNDR1: And then if you try to do something, some setting, it started ******** that it's not extended and it was in the middle of testing.
Okal Jan GNL SCNDR1: So in some of the automated transactions, it failed in the middle or for example with with a mobisys.
Okal Jan GNL SCNDR1: That's just one example, and we could just extend all the materials to every single location.
Okal Jan GNL SCNDR1: It, as far as I know, it doesn't hurt anything if it's extended.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Any excuse me?
Ashmore Matt NRT SCNDR1: Any questions or or concerns anyone would like to bring up?
Stevaux Jorge SRC SCNDR1: No, we have to make a draft and then from there we have a start point.
Stevaux Jorge SRC SCNDR1: There will be.
Stevaux Jorge SRC SCNDR1: We can't get 100% done now, but we know a lot that we can add in the first time, you know.
Okal Jan GNL SCNDR1: Ah, yeah.
Okal Jan GNL SCNDR1: And if we can set it, we did something and we missed the 5%.
Okal Jan GNL SCNDR1: It's fine, but we should not miss 100.
Ashmore Matt NRT SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: OK guys.
Ashmore Matt NRT SCNDR1: Again, if there's nothing else then then please use the timer on Friday to go through that Excel and add some some.
Ashmore Matt NRT SCNDR1: Requirements that you know that you know about and provide them to Tiago.
Cabrera Abraham EXT Robert Bosch Mexico: Sure.
Segura Jose Angel MNR SCNDR1: OK, sure.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: And sorry, go ahead.
Stevaux Jorge SRC SCNDR1: Is is this file like shared that we each one gonna go there and edit?
Stevaux Jorge SRC SCNDR1: Or does this like everyone makes?
Stevaux Jorge SRC SCNDR1: Maybe makes a draft and send it whatever we changed it to Tiago and he compiles.
Ashmore Matt NRT SCNDR1: Whatever you got, I just added it in the chat.
Ashmore Matt NRT SCNDR1: I mean, we can put it somewhere that is shared if that's what you guys want to do, that's fine.
Ashmore Matt NRT SCNDR1: It's it's really up to you.
Okal Jan GNL SCNDR1: Yeah, it just making a change.
Okal Jan GNL SCNDR1: Whatever you think and then send it to Thiago and me if you can.
Okal Jan GNL SCNDR1: Ohh you can copy others so they see.
Stevaux Jorge SRC SCNDR1: OK, what?
Ashmore Matt NRT SCNDR1: It doesn't.
Ashmore Matt NRT SCNDR1: Doesn't hurt.
Segura Jose Angel MNR SCNDR1: OK.
Stevaux Jorge SRC SCNDR1: Alright.
Ashmore Matt NRT SCNDR1: Alright, well, if there's nothing else I want to say thank you guys, I'll course will also speak again tomorrow.
Ashmore Matt NRT SCNDR1: But thank you guys and and I really think this this exercise will help us in the upcoming UAT.
Stevaux Jorge SRC SCNDR1: It will, I agree.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: Alright, well have a good day, Christina and maybe Jan if you can stay, I want to show you something that I learned earlier today.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: But bye bye.
