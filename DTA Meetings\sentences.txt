<PERSON><PERSON><PERSON> Jens FRD SCNDR: So I invited here all the divisional process experts from Division U and from Division C to that meeting, just to have the same understanding and have the same information share.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: So I don't want to, umm, support any building of silos we work hand in hand and that should be our common understanding.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: Everybody is sitting at the same table and everybody has his or her role to play in that project.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: So this here is an agreement what we made a couple of months ago already on the example of Division C but accepted by the this CMYC our SCM Steer Co and by the MSC for the purchasing colleagues.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: So this is just an overview overview I presented it already last meeting which is so role of the divisional process expert here in our program.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: So I don't want to see any body saying OK, this is not my job.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: This is not my job.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: We all are sitting at the same table.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: We all have the same target to make our ERP program roll out happen, but for sure, as I said, everybody has a different role.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: So I T guys have a role.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: DPE S have a role DTA's have a role and so on, and my expectation is that we all work on ice level as a good partnership together to make it happen.
<PERSON>eler Jens FRD SCNDR: This is only as a friendly reminder, nothing to complain and nothing to go in detail, but just to make sure everybody has the same understanding.
Hadeler Jens FRD SCNDR: Second topic is more functional topic.
Hadeler Jens FRD SCNDR: What I would like to share so in some projects discussions came up about prototyping and there is no process described that is not defined.
Hadeler Jens FRD SCNDR: How to deal with that and I just want to use this opportunity to sharpen that a little bit and explaining umm whole prototyping should be handled.
Hadeler Jens FRD SCNDR: So in general, you can read through all that documents, all that is shared on the SharePoint stored on the SharePoint.
Hadeler Jens FRD SCNDR: So everybody has access to it.
Hadeler Jens FRD SCNDR: Yeah, just a moment.
Hadeler Jens FRD SCNDR: I've seen your hand, so we have an early prototyping.
Hadeler Jens FRD SCNDR: In general, what is without inventory management?
Hadeler Jens FRD SCNDR: That is the key message and we have a professional prototyping and this is then the serial production process.
Hadeler Jens FRD SCNDR: That means including EWM, including everything full factory process, and there is no special really, no special prototyping process in logistics in the area of operations means how to handle a production order.
Hadeler Jens FRD SCNDR: There is a difference, but this is for example dedicated work routings dedicate for other machines like described here.
Hadeler Jens FRD SCNDR: Prototype machines prototype employees and so on.
Hadeler Jens FRD SCNDR: Yeah, but this does not affect all the logistics and purchasing flow.
Hadeler Jens FRD SCNDR: Yeah, we have only black or white means without inventory management.
Hadeler Jens FRD SCNDR: Then we have free text purchase requisitions, or we have the full blown factory process.
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: So now Lingpeng your comment or your question.
Zeng Lingpeng SGH COOA: Yeah.
Zeng Lingpeng SGH COOA: I just want to remind you, if you have already thought your you're running your your your pictures because my I I my screen fixed on the agenda page.
Hadeler Jens FRD SCNDR: Ah, OK I'm I'm on the wrong.
Hadeler Jens FRD SCNDR: Why is that still wrong if me a second?
Hadeler Jens FRD SCNDR: So now the right screen OK.
Hadeler Jens FRD SCNDR: Hey.
Zeng Lingpeng SGH COOA: I will overview prototyping process variant.
Zeng Lingpeng SGH COOA: OK, alright.
Hadeler Jens FRD SCNDR: OK, so this is discussed in the Junjun project already, but it it could be or it is.
Hadeler Jens FRD SCNDR: It is a benefit to all projects to have the same understanding here so that we have this differentiation between early and professional prototyping and this document or this page.
Hadeler Jens FRD SCNDR: This slide helps to explain the difference to to understand the difference more and not to search for a solution that is not existing.
Hadeler Jens FRD SCNDR: Yes, Jose, there is nothing in between.
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: Good.
Hadeler Jens FRD SCNDR: Any questions regarding this prototyping at the moment.
Hadeler Jens FRD SCNDR: Then I have one more information.
Hadeler Jens FRD SCNDR: What?
Hadeler Jens FRD SCNDR: Uh drives many of you.
Hadeler Jens FRD SCNDR: That is the MES and SCADA systems handling.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So that is a big elephant, as you see here.
Hadeler Jens FRD SCNDR: MES is really a huge topic.
Hadeler Jens FRD SCNDR: Uh, and this year?
Hadeler Jens FRD SCNDR: Yeah, this small man that is us.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So and we need to be a little bit careful with how to handle this big elephant and therefore already some weeks ago we started discussing who is doing what with that with that regards and this is a was a long but in and intensive discussion with the colleagues from operations and.
Hadeler Jens FRD SCNDR: Example with Division C colleagues here and with the production IT colleagues and the result you see here on the left hand side we will not go through this, but I have added here the link to the official document.
Hadeler Jens FRD SCNDR: What is the approved version and I just want to let you know here in that meeting we have discussed that it is defined who is doing what and to give you a better orientation in the project work how to handle this elephant.
Hadeler Jens FRD SCNDR: Yeah, please have a look into this document and go through it in detail.
Hadeler Jens FRD SCNDR: And if you have questions in concrete projects how to handle this and that, then please contact Bernhard who is here in the call and visit the Wednesdays expert group sessions where these topics can be discussed and where experts speak to experts.
Hadeler Jens FRD SCNDR: And yeah, find a solution, or if it is no solution available, start an escalation process.
Hadeler Jens FRD SCNDR: In general, the responsibilities are we as domain S continue to be responsible for the processes in SAP and mobisys the decision making about the future Emmy as system per rollout project is a divisional decision in operations.
Hadeler Jens FRD SCNDR: The MES and SCADA system runs uh projects run as separate project means with separate budget and separate resource.
Hadeler Jens FRD SCNDR: Plus the synchronization between this separate project and the and the SAP project needs to be made sure by domain O.
Hadeler Jens FRD SCNDR: But that means by the DTA's in this area that is common understanding and we are supporting here, we need to support, we need to participate to that meetings and we need to actively contribute.
Hadeler Jens FRD SCNDR: But the lead is within operations.
Hadeler Jens FRD SCNDR: That is the key message here and that hopefully helps you to better understand the complexity and better handling the complexity.
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: Then we have already sown this slide here with the projects starting next year in April and May.
Hadeler Jens FRD SCNDR: Umm, this is a huge range of projects.
Hadeler Jens FRD SCNDR: I always repeat, this is a plan.
Hadeler Jens FRD SCNDR: This is the plan communicated by the PMO.
Hadeler Jens FRD SCNDR: If this plan would be made to reality depends on us depends on the team building depends on the process development, has many many influencing factors and especially one factor is to understand the complexity of these sites as early as possible.
Hadeler Jens FRD SCNDR: That means not only when the integration when the completeness check will start, but starting earlier with understanding the complexity, making basic decisions like the manufacturing type decision, starting complex topics like understanding customer requirements earlier and so on so that we can start with the project work here in a efficient way.
Hadeler Jens FRD SCNDR: And this pretext, I have one more slide here.
Hadeler Jens FRD SCNDR: There is a communicated timeline how that will work and we are here.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So that means there are questionnaires prepared and these questionnaires have been sent out via the process management organization to the plans and we expect to get feedback ideally by end of this week.
Hadeler Jens FRD SCNDR: If this is realistic, we have to see and then we start analyzing these and organizing deep dive sessions and for domain as it is agreed that the the check the first check of this received questionnaires will be done by the regional lead DTA as so and the outputs the feedback will be aligned.
Hadeler Jens FRD SCNDR: Then in deep dive sessions with the PMO by me, and if we need deep dive sessions here and there punctually, then we arrange that with the divisional process experts so that we work as again hand in hand to go forward, have a common understanding and sets the right priorities in order to prepare the project kick offs next year.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So you can go through this timing here and and see what is planned by whom and when.
Hadeler Jens FRD SCNDR: But in general, I guess this activity has a clear benefit.
Hadeler Jens FRD SCNDR: The only topic and important topic what we need to make sure is we have definitively a capacity conflict with the ongoing all out projects and here we need to find a smart way how to deal it, how to deal with it.
Hadeler Jens FRD SCNDR: And I'm positive that we will do it and make it.
Hadeler Jens FRD SCNDR: So next question is a topic where the journey goes to.
Hadeler Jens FRD SCNDR: That means digital transformation is nothing that effects only the plants that effects us as well.
Hadeler Jens FRD SCNDR: And that means we need to come closer together.
Hadeler Jens FRD SCNDR: We need to collaborate more in this digital transformation as today, and that means we started with functional allocation.
Hadeler Jens FRD SCNDR: We started with functional experts and our journey, what we need to go through is that we become that everybody of us becomes more an end to end expert.
Hadeler Jens FRD SCNDR: That means the future, the future expectation, not for today, not for tomorrow.
Hadeler Jens FRD SCNDR: The future expectation is that we substitute these cross functional topics here.
Hadeler Jens FRD SCNDR: For example, performance management or container management, where one functional person is in the lead and the others support that we more come in the future to an approach of focusing on end to end processes.
Hadeler Jens FRD SCNDR: That means per project next year.
Hadeler Jens FRD SCNDR: Ideally, let's let's check if that will be possible.
Hadeler Jens FRD SCNDR: Our target is that one person takes the responsibility of 1 end to end process that avoids many, many interfaces.
Hadeler Jens FRD SCNDR: But on the other hand side, it needs more collaboration because the deep knowledge is not yet there for everybody.
Hadeler Jens FRD SCNDR: This is why we said this is a target picture.
Hadeler Jens FRD SCNDR: This is a future, but I would like to share this future plan with you so that we all can work on achieving the same target.
Hadeler Jens FRD SCNDR: I guess it is important to understand where the journey goes to so that everybody understands how he or she can contribute to that.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: Any question?
Meschede Jan SCW SCNDR: This is young speaking.
Meschede Jan SCW SCNDR: Should we align with our lead TA's?
Meschede Jan SCW SCNDR: On which end to end process we can concentrate or try to learn.
Meschede Jan SCW SCNDR: Or is it on our own?
Hadeler Jens FRD SCNDR: So we have already the qualification matrix in the qualification matrix.
Hadeler Jens FRD SCNDR: We have a documented training plan.
Hadeler Jens FRD SCNDR: That means who needs to learn what?
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So that means if we adjust here and there the qualification matrix, then the way forward becomes clearer and clearer.
Hadeler Jens FRD SCNDR: So it's not the expectation to make it crystal clear that everybody is able to cover everything by next week, Tuesday or so, yeah.
Hadeler Jens FRD SCNDR: So this is a future way, but the date of achieving that is not yet defined.
Hadeler Jens FRD SCNDR: But this is the way the target where it goes to.
Meschede Jan SCW SCNDR: So we start before we need to.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So and you have seen in the integration test and you will see in the user acceptance test we some of you already took the responsibility being an end to end test case coordinator, this is exactly the logical consequence out of this.
Hadeler Jens FRD SCNDR: So the journey has already started.
Hadeler Jens FRD SCNDR: OK.
Meschede Jan SCW SCNDR: Thank you.
Hadeler Jens FRD SCNDR: But so and then I have one last topic and that is an important topic.
Hadeler Jens FRD SCNDR: So there are many rumors in the organization.
Hadeler Jens FRD SCNDR: Hey, what is with the ERP program?
Hadeler Jens FRD SCNDR: How much will it grow?
Hadeler Jens FRD SCNDR: How many people do we need?
Hadeler Jens FRD SCNDR: And so on.
Hadeler Jens FRD SCNDR: And I just want to avoid any misunderstanding here and be very transparent with you.
Hadeler Jens FRD SCNDR: So in we have a plan how to uh, stuffs the teams in the future and you have seen that next year 18 projects are planned to start and one important pillar is the staffing of these teams.
Hadeler Jens FRD SCNDR: That means having the experts available to do it, because without our knowledge, without you, without the team, this will not be possible and I just want to share this topic here with you.
Hadeler Jens FRD SCNDR: We have 3 batches.
Hadeler Jens FRD SCNDR: That have been approved by the Board of Management and we have to staff our teams by end of this year to make A to ensure the project starts in 2025.
Hadeler Jens FRD SCNDR: At the moment 59 positions are open.
Hadeler Jens FRD SCNDR: That means we search for 59 people, 59 build PE and DTA S in domain S.
Hadeler Jens FRD SCNDR: Yeah, if these people come from IT, come from a plant, come from a business unit, from a division or wherever, doesn't matter and they need, they don't need to move to demain domain S, but we need 59 people to contribute to the project work.
Hadeler Jens FRD SCNDR: This is the important message to make the project work happen and what is important here is the minimum and the ideal required skills.
Hadeler Jens FRD SCNDR: You all know that from your project work, but what is important here to understand?
Hadeler Jens FRD SCNDR: I would like to ask you everybody here in the call to look left and right.
Hadeler Jens FRD SCNDR: If you find people that want to be promoted that have the skills, but by whatever reasons, they will not be promoted and worst case we might lose them to the market so that they will leave the F, we should avoid that.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: And if you have people like this, if you know people like this, if you know colleagues that for example will be affected by restructuring by plant closures and so on, yeah.
Hadeler Jens FRD SCNDR: Or hidden talents.
Hadeler Jens FRD SCNDR: Talents that nobody sees but people that know a lot and want to contribute but nobody sees them and nobody promotes them.
Hadeler Jens FRD SCNDR: Then please please give a hint.
Hadeler Jens FRD SCNDR: Yeah, so not give a hint that we will never do direct marketing.
Hadeler Jens FRD SCNDR: We will never contact them directly to say hey, would you please move to that role?
Hadeler Jens FRD SCNDR: Never.
Hadeler Jens FRD SCNDR: Campaign meetings.
Hadeler Jens FRD SCNDR: What we are going to organize across all the world and in all countries where we search ddas and build PE's so that they can hear and listen to people that have already the experience and maybe this will help the people to make a decision if set whole might be interesting for them. Yeah.
Hadeler Jens FRD SCNDR: So no direct marketing, no active head hunting.
Hadeler Jens FRD SCNDR: But if you know people where we have the risk that they believe the F if they if you know hidden talents that might be interested in something like this, if a hint and we will invite them to these so called next step campaigns organized by Global Recruiting center and then they can make their own decision if they are still interested or not.
Hadeler Jens FRD SCNDR: And this was all my presentation for today.
Hadeler Jens FRD SCNDR: Any more questions?
Hadeler Jens FRD SCNDR: No.
Hadeler Jens FRD SCNDR: If not, then I would like to hand over to machine for a short status update from the build perspective.
Makan Marcin SND SCNDB: Very good.
Makan Marcin SND SCNDB: So I hope you can hear me and see my slides.
Makan Marcin SND SCNDB: Can you?
Makan Marcin SND SCNDB: So I have prepared 10 slides and I will give you a quick update.
Makan Marcin SND SCNDB: Where do we stand on 24.1 and 25.1 from the build perspective?
Makan Marcin SND SCNDB: So first of all, looking at the overall picture in 24.1, as you can see, SCM including customs is the only one area only domain that has still some open tickets.
Makan Marcin SND SCNDB: We have 12 of them on SCM side and three on uh customs and I will also explain on one of the next slides what is the major reason for this and that's the slide.
Makan Marcin SND SCNDB: So I was analyzing the situation just quickly and we can see that in April of the last year, which was the month right after the scope closure of 24.1, we only had 50 tickets.
Makan Marcin SND SCNDB: When we take a look at the amount of tickets that has been created since then until July and we have doubled this number, so we have achieved 200% of increase over this period of time and I just wanted to look at a theoretic scenario and I saw that and where if we just and did not increase the number of tickets from October last year on, we would have been already ready for quite some time.
Makan Marcin SND SCNDB: Meanwhile, no.
Makan Marcin SND SCNDB: So the main message here is we must avoid increasing or inflating tickets at any cost, because this is the major point that is driving our backlogs.
Makan Marcin SND SCNDB: To continue on 24.1 in the area of label and EDI tickets, we have 8 remaining tickets, her and all of them will be finished before go live and even this Honda, where the biggest focus currently is on because of the highest workload that is still in front of us.
Makan Marcin SND SCNDB: To continue with the outbound process tickets in the outbound area we have.
Makan Marcin SND SCNDB: Six tickets altogether.
Makan Marcin SND SCNDB: And three of them are late finding.
Makan Marcin SND SCNDB: Three are nota fiscal and also working with the highest focus.
Makan Marcin SND SCNDB: With the help of everybody here, IT business DTA's and Victor from from integration specialist office working to finalize them either before user acceptance test or in the first week of user acceptance.
Makan Marcin SND SCNDB: This so here we for sure know that several tickets will not be finished and before starting off user acceptance test and especially in the outbound area and here I believe we have an agreement to not test those affected processes.
Makan Marcin SND SCNDB: Before their finalization.
Makan Marcin SND SCNDB: Because then the outcome is clear and it it is negative.
Makan Marcin SND SCNDB: So we should test them only when they are done.
Makan Marcin SND SCNDB: In order to achieve.
Makan Marcin SND SCNDB: A positive outcome, right?
Makan Marcin SND SCNDB: So obviously it is not.
Makan Marcin SND SCNDB: It is to on one hand side to make sure that all the scopes, all the functions are ready because we are speaking about very critical area like outbound.
Makan Marcin SND SCNDB: But on the other side, to enable to receive positive result of testing by testing them later during the user acceptance test.
Makan Marcin SND SCNDB: Customs tickets there is three of them in 24.1 remaining uh, where the absolute focus is on the software.
Makan Marcin SND SCNDB: A solution this is Brazil.
Makan Marcin SND SCNDB: This is necessary for Brazil also working with the highest focus here and obviously the UAT also starts right now meaning on the 29th uh.
Makan Marcin SND SCNDB: But we have this situation with with ECHO where they are go life is by the 1st of January.
Makan Marcin SND SCNDB: And that's the last slide on 24.1, also open EWM and production logistics tickets.
Makan Marcin SND SCNDB: So here our absolute top ticket is right now this 13990, which has been also.
Makan Marcin SND SCNDB: Umm, a late finding, which is a late finding.
Makan Marcin SND SCNDB: This is about the mobisys adjustments and this is also a ticket that is needed as a baseline for the Colpack ticket in 25.1.
Makan Marcin SND SCNDB: So this one we must finish in priority.
Makan Marcin SND SCNDB: And we have had calls in the recent days upon which we now know that this adjustments these adjustments are gonna be done.
Makan Marcin SND SCNDB: Uh.
Makan Marcin SND SCNDB: By the end of next week, so still two weeks more or less to now and on production logistics mainly nota fiscal and subcontracting topics which are also relevant for.
Makan Marcin SND SCNDB: Uh, for echo?
Makan Marcin SND SCNDB: For limeira.
Makan Marcin SND SCNDB: Moving on to 25.1, uh, as we can see from the overall status, this does not include labels and EDI.
Makan Marcin SND SCNDB: Also, as we can see, as see I am A has been and still is behind all other work streams.
Makan Marcin SND SCNDB: Although we are making progress and being better and better every week and the main reason is, of course the massive backlogs from 24.1.
Makan Marcin SND SCNDB: Also would like to share with you the newly received tickets in 25.1.
Makan Marcin SND SCNDB: So meanwhile, we have definitely exceeded the critical mass.
Makan Marcin SND SCNDB: It was not yet the case last month.
Makan Marcin SND SCNDB: This this time we have already exceeded.
Makan Marcin SND SCNDB: And so we have received 19 uh tickets from plants.
Makan Marcin SND SCNDB: Now I'm not counting a strategic tickets here.
Makan Marcin SND SCNDB: I'm not counting labels and EDI tickets.
Makan Marcin SND SCNDB: These are process tickets.
Makan Marcin SND SCNDB: Uh, they are listed here and this makes just roughly counting something like 380 days of additional business effort and the same on idea for since we know that these two efforts are mostly the same.
Makan Marcin SND SCNDB: Uh, so also here we are already in in the let's say red flag situation here.
Makan Marcin SND SCNDB: So uh request goes to uh everybody to not increase tickets any further and we will not be able anyways to finalize all of them until and until a package three which as we know the deadline was already on the 19th and now we are fighting to finalize within package 4.
Makan Marcin SND SCNDB: And here you can see the split of SCM tickets into packages.
Makan Marcin SND SCNDB: So finalizing package 3IN package four, we have still 2423 tickets.
Makan Marcin SND SCNDB: However, these are not integration test relevant because we moved all the integration test relevant tickets into package 3, knowing that we will not finish them.
Makan Marcin SND SCNDB: Yeah, in time.
Makan Marcin SND SCNDB: Meaning until July 19th.
Makan Marcin SND SCNDB: But we will still manage to finish them before and integration test build which starts at the beginning of September and this is the slide showing, sorry.
Hadeler Jens FRD SCNDR: Sorry, nothing.
Makan Marcin SND SCNDB: And this is the slide showing 25.1 tickets at risk.
Makan Marcin SND SCNDB: There was eight of them.
Makan Marcin SND SCNDB: Six are in EWM, 2 are are in outbound processes, so we have definitely now significantly changed situation because you may remember that in 24.1 we had outbound processes definitely on the on the critical path.
Makan Marcin SND SCNDB: Now it is EWM sub workstream data is on the critical path and two of them are already in the queue.
Makan Marcin SND SCNDB: Check and one is really, really critical from the build point of view, not from the rollout point of view yet.
Makan Marcin SND SCNDB: That's CUSTOMIZING for INTERCOMPANY planning in in EWM where we see the due date is already 6th of September which impacts definitely the integration test build.
Makan Marcin SND SCNDB: As I stated here, but of course not yet impact impact on the integration test on the rollout side.
Makan Marcin SND SCNDB: So we will do everything.
Makan Marcin SND SCNDB: What is in our power?
Makan Marcin SND SCNDB: You know, in our influence to be able to finish also this ticket earlier to to improve things.
Makan Marcin SND SCNDB: So we are tracking all the tickets now very, very closely.
Makan Marcin SND SCNDB: And as you can see from as you can tell from the dates, they are not yet so critical, at least not from the integration test point of view.
Makan Marcin SND SCNDB: Yeah, that's it from my side.
Makan Marcin SND SCNDB: Any questions or remarks?
Hadeler Jens FRD SCNDR: I have one important remark message here to the whole team.
Hadeler Jens FRD SCNDR: The house is burning.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: And if you see, if we all see that see house burns.
Hadeler Jens FRD SCNDR: Then we should not put additional oil into it.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: And we should take every material out that is able to burn.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So that means in concrete.
Hadeler Jens FRD SCNDR: If we can offer alternatives instead of creating new tickets, even if the new ticket is a valid requirement, and even if the new ticket is better than the available standard functionality, I hope with that overview everybody is able to understand the situation a little bit better.
Hadeler Jens FRD SCNDR: Why we are fighting all the day to reduce tickets to avoid new tickets.
Hadeler Jens FRD SCNDR: The best ticket is a ticket that is not existing.
Hadeler Jens FRD SCNDR: And please everybody help to stop this fire.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So I just want to take an example without saying names and without saying projects.
Hadeler Jens FRD SCNDR: But if we print out documents we haven't available solution to print out documents from one system and that works.
Hadeler Jens FRD SCNDR: Maybe it has advantages, maybe it has disadvantages.
Hadeler Jens FRD SCNDR: Why do we create a new ticket?
Hadeler Jens FRD SCNDR: To change the way of printing, why do we add additional information to a interface where we have a workable solution that is maybe not so nice and not so fancy?
Hadeler Jens FRD SCNDR: Yeah, but this is definitively the wrong point of time.
Hadeler Jens FRD SCNDR: To raise these requirements and to work on Nice to haves and because I want to have it strategically this way or that way, the house is burning.
Hadeler Jens FRD SCNDR: And we need to make sure that this fire doesn't affect every individual of us.
Hadeler Jens FRD SCNDR: And this is only possible if everybody looks into the mirror and asking hey, can we live with the alternative?
Hadeler Jens FRD SCNDR: Maybe it's 80% of the ideal process, but is it possible to survive with the alternative?
Hadeler Jens FRD SCNDR: And if we answer this question, yes it is possible.
Hadeler Jens FRD SCNDR: Then please canceled the tickets.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: Cancers them not asking, hey, do we have a bottleneck?
Hadeler Jens FRD SCNDR: Hazy houses burning everywhere.
Hadeler Jens FRD SCNDR: That is not only one room that is burning the house burns.
Hadeler Jens FRD SCNDR: And putting additional oil into the house, we're not make the problem smaller.
Makan Marcin SND SCNDB: I cannot say it better than than you have just done it and you know I was fighting for this as well for exactly this case, but.
Makan Marcin SND SCNDB: I got overruled.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So it's, it's not about hey, the GPO has approved and we need to make it.
Hadeler Jens FRD SCNDR: There is simply no capacity to do it.
Hadeler Jens FRD SCNDR: Not limited, no capacity.
Hadeler Jens FRD SCNDR: That is the message.
Hadeler Jens FRD SCNDR: What we need all to understand, and this is a question here to the group, is there any buddy that has a question on that where we need to get more clarity on then please raise your hand or give me or marching a call afterwards.
Hadeler Jens FRD SCNDR: But this is not this is the message here.
Hadeler Jens FRD SCNDR: This is not the time for for games that is not the time for I need to have that.
Hadeler Jens FRD SCNDR: We should simply cancel the ticket, not postponing it to the future.
Hadeler Jens FRD SCNDR: Cancel it.
Hadeler Jens FRD SCNDR: Put it away.
Hadeler Jens FRD SCNDR: OK.
Makan Marcin SND SCNDB: I guess uh, the same message should go to GPO's and lead PE's, no?
Hadeler Jens FRD SCNDR: Tomorrow we will have the SCM Steerco and we will place the same message there.
Makan Marcin SND SCNDB: I have done so already.
Makan Marcin SND SCNDB: No thanks.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: Good.
Hadeler Jens FRD SCNDR: But we count on everybody here in the car.
Hadeler Jens FRD SCNDR: We count on you.
Hadeler Jens FRD SCNDR: This is nothing.
Hadeler Jens FRD SCNDR: What can be solved by management that can only be solved by a common activity and everybody of us is the same important?
Makan Marcin SND SCNDB: Now because, uh, you as you as you just said.
Makan Marcin SND SCNDB: Uh, and I know what what the other party would say that they would say, but we also need to bring improvements to the plants, right?
Makan Marcin SND SCNDB: This is then they say this is what the template is all about now.
Hadeler Jens FRD SCNDR: Yes, but takes a simple example.
Hadeler Jens FRD SCNDR: What I just meant if the print out is left or right, it is the same printout, so it doesn't make a difference.
Hadeler Jens FRD SCNDR: There is advantages and disadvantages and this is hey, not the right time to discuss things like that.
Makan Marcin SND SCNDB: Yep.
Makan Marcin SND SCNDB: Yes.
Hadeler Jens FRD SCNDR: OK, good.
Hadeler Jens FRD SCNDR: Any more questions to machines presentation?
Hadeler Jens FRD SCNDR: If not, then I would like to hand over to Kasia.
Gendek Katarzyna CZS SCNDR: Sure.
Gendek Katarzyna CZS SCNDR: So I have to topics on the agenda today and 1st of the topic is the.
Gendek Katarzyna CZS SCNDR: Status of auto.
Gendek Katarzyna CZS SCNDR: Task Force group.
Gendek Katarzyna CZS SCNDR: So let me share my screen with you.
Gendek Katarzyna CZS SCNDR: Please let me know when my screen will be visible for you guys.
Meschede Jan SCW SCNDR: It is now.
Gendek Katarzyna CZS SCNDR: OK, perfect.
Gendek Katarzyna CZS SCNDR: So first topic from the agenda is the status of O2 C task for the group.
Gendek Katarzyna CZS SCNDR: So as TM outbound Task Force group, how we call it and this is status from today.
Gendek Katarzyna CZS SCNDR: So as you probably know, we have some small working group that have a call every day leaded by Victor Prokopczuk integration specialist.
Gendek Katarzyna CZS SCNDR: Where someone is testing 1 by 1 on the scenarios from champagne plant, which is the most complex plant of 24.1 plants in Albany area, and then together some configuration issues or some problematic situation we are trying to solve.
Gendek Katarzyna CZS SCNDR: So to my if making incredible, incredible job with this testing and here's the status.
Gendek Katarzyna CZS SCNDR: So first of all, in one of the Zhangjiagang plans, because we have a few reporting units in Jantan all main outbound scenarios where Texas fully tested in GUI and Fiori and other plants that starts in gum all main outputs like picklist delivery node, even the loading list has been tested successfully.
Gendek Katarzyna CZS SCNDR: Now the open topic or the topic in progress is about the repacking and labels printing after repacking, but it looks like it's only about missing configuration that Evan and the other topic in progress is about the returns from 3 PL warehouse or from customer via three PL warehouse to the plant.
Gendek Katarzyna CZS SCNDR: Umm.
Gendek Katarzyna CZS SCNDR: Stock under the quality blocked status from 3 PL to plant via SPRO will be tested very very soon, but we do not see a risk in here.
Gendek Katarzyna CZS SCNDR: The next completed topic is the I'm missing.
Gendek Katarzyna CZS SCNDR: Customizing that were detected like RFC.
Gendek Katarzyna CZS SCNDR: Umm, but we are still facing issue with some kind of missing transport.
Gendek Katarzyna CZS SCNDR: Uh, luckily, we have always Vivien yeska in our calls.
Gendek Katarzyna CZS SCNDR: So she is checking the status of the transports, so we mark this topic as in progress and then we are going to topics that are marked with red.
Gendek Katarzyna CZS SCNDR: But margin already mentioned the same topic.
Gendek Katarzyna CZS SCNDR: So as you can see, we have now that topics about the mobisys.
Gendek Katarzyna CZS SCNDR: First of all, it's the mobisys training which is more or less completed without one application and this application is the picking warehouse.
Gendek Katarzyna CZS SCNDR: That's confirmation it was mentioned by Martin.
Gendek Katarzyna CZS SCNDR: This is a late finding.
Gendek Katarzyna CZS SCNDR: The ticket created by Victor Umm as after picking we need to confirm the warehouse task at the beginning the application was created as a copy of bin to line Mobisys application.
Gendek Katarzyna CZS SCNDR: But it was recognized during the testing that is not fulfilled the requirement because it's working only with very simple scenario like 1 pallet.
Gendek Katarzyna CZS SCNDR: But when we have more pallets or.
Gendek Katarzyna CZS SCNDR: Repacking or maybe colpack this application is simply not working.
Gendek Katarzyna CZS SCNDR: That's why this ticket is created and the application is done from the scratch.
Gendek Katarzyna CZS SCNDR: So completely redesigned we get the due date which is the 2nd of August, which is maybe not the best for us, but the best that template can offer us with the full capacity of 1 developer.
Gendek Katarzyna CZS SCNDR: And even if this is marked at risk, I strongly believe that this will be still tested during UAT because if this will be transported to the T1 immediately after there are realization, it will be only after 3-4 days of the UAT.
Gendek Katarzyna CZS SCNDR: So we will still have a possibility to test it within the standard shipping scenarios, so it's still marked at risk because this is under development and once this will be developed and transport.
Gendek Katarzyna CZS SCNDR: We believe that this will be possible to be tested during the UAT and the status will will change, so we will avoid this risk.
Gendek Katarzyna CZS SCNDR: So this is the status of the O2 C Task Force group.
Gendek Katarzyna CZS SCNDR: Do you have any kind of questions to the to the status or to the working mode of a SIM outbound task force group?
Ashmore Matt NRT SCNDR1: Maybe Ke$ha.
Ashmore Matt NRT SCNDR1: One question how does this apply to the other projects such as Fenton, Fowlerville, Echo that are in 24.1?
Gendek Katarzyna CZS SCNDR: It's a very good question and this was even raised today by Jens during the status call.
Gendek Katarzyna CZS SCNDR: So yes, at the moment during the task force, we are completing the chance again as the most complex plant because we believe that most of the scenarios are being covered by Tantra, gonna plant and then should work with other plants as well.
Gendek Katarzyna CZS SCNDR: But we are missing the knowledge transfer, so we agreed during the status call today with Victor and I already also contact the Paula that Paula chosen the scenarios that are in the Phantom 4 wheel plant, the most critical 1 scenarios and we will cover the knowledge transfer and experience transfer from the task force on Thursday.
Gendek Katarzyna CZS SCNDR: We have a 1 1/2 hour umm training on Thursday that will be needed by myself and Victor where we will try to cover as much as possible from the Phantom 4 wheel main scenarios and then we will support Pawel during the dry runs.
Gendek Katarzyna CZS SCNDR: In that one, we believe that it will be already in that one, not in that one.
Ashmore Matt NRT SCNDR1: Thank you.
Hadeler Jens FRD SCNDR: So there is a raised hand from Yang.
Gendek Katarzyna CZS SCNDR: Yeah.
Okal Jan GNL SCNDR1: I have one question.
Okal Jan GNL SCNDR1: Uh, you say that the outputs are completed.
Okal Jan GNL SCNDR1: Did you achieve any physical printout from ZE1?
Okal Jan GNL SCNDR1: Or is it only creation of the spools?
Okal Jan GNL SCNDR1: Did you?
Gendek Katarzyna CZS SCNDR: Creation of the sports at the moment because so my cannot physically connect with the printer in Chandrakanta moment.
Okal Jan GNL SCNDR1: Yeah, because I had the same issue.
Okal Jan GNL SCNDR1: There's not option to physically print from ZE1.
Okal Jan GNL SCNDR1: Apparently there's only one OHS printer server connected to ZT one, and cannot handle both.
Okal Jan GNL SCNDR1: At least that's my understanding.
Okal Jan GNL SCNDR1: OK, so it was only spools, not physical prints.
Okal Jan GNL SCNDR1: OK, thanks.
Gendek Katarzyna CZS SCNDR: Unfortunately, some kind of artificial system where the standard transport has been triggered manually and without connection with for example, win two, et cetera.
Gendek Katarzyna CZS SCNDR: So this may be a reason why the physical printouts are not working as well in that one during the previous testing.
Gendek Katarzyna CZS SCNDR: I know that the printouts were possible.
Gendek Katarzyna CZS SCNDR: I hope that in your case as well you were able to print out during the integration test the the, the one uh, so it should work the same way, uh, right now when we will switch back from that you want to that one because that one is only for us for continue testing for continued gaining knowledge.
Gendek Katarzyna CZS SCNDR: But this is not the system where we are performing the UAT test cases.
Gendek Katarzyna CZS SCNDR: We are switching back to that one, which is better for us because it's with life transports from that Q1 with all the connections to the external system.
Gendek Katarzyna CZS SCNDR: So we are we rely on that one.
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: Does that answer your question, Yan?
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: Any more questions?
Hadeler Jens FRD SCNDR: Then Kasia, please go ahead with the Customs portion.
Gendek Katarzyna CZS SCNDR: So let's let me switch my presentation to the second one to the second topic from the agenda.
Gendek Katarzyna CZS SCNDR: And once again, please let me know when my screen will be visible again.
Gendek Katarzyna CZS SCNDR: OK, perfect.
Gendek Katarzyna CZS SCNDR: So the second point in the agenda assigned to myself is the custom system integration and overview.
Gendek Katarzyna CZS SCNDR: Umm, where Thorsten Global should support me.
Gendek Katarzyna CZS SCNDR: Unfortunately, he is not.
Gendek Katarzyna CZS SCNDR: He was not able to join today's meeting, but we have Harald from the Customs global customs management with us.
Gendek Katarzyna CZS SCNDR: So if you will have any questions after this presentation, please do not hesitate to ask.
Gendek Katarzyna CZS SCNDR: I hope that myself and Harald will be able to answer more Harald than myself, but I hope that every questions could be done, answered umm.
Gendek Katarzyna CZS SCNDR: So as it was shown by yes, yes, during today's presentation, one of the slides, uh, you are aware that the Customs interface are part of the outbound DT activity activities and in future of end to end order to cache scenario.
Gendek Katarzyna CZS SCNDR: And we are not only preparing, but we are start running this joint project of S4 HANA and custom system implementation.
Gendek Katarzyna CZS SCNDR: You can hear about MIC custom systems, but we are avoiding right now to calling it only MIC integration with sore HANA because we have more custom systems.
Gendek Katarzyna CZS SCNDR: So let me go through the customs department.
Gendek Katarzyna CZS SCNDR: The responsibility where they are touching with S4 HANA project and how DTA especially outbound DTA's will be.
Gendek Katarzyna CZS SCNDR: Affected by this?
Gendek Katarzyna CZS SCNDR: Responsibility of the Customs implementation.
Gendek Katarzyna CZS SCNDR: OK so here you can see the main processes in the Customs domain which are splitted into three groups.
Gendek Katarzyna CZS SCNDR: So we have ensure customs compliance where we have topics like import, export declaration, broker management and FDA.
Gendek Katarzyna CZS SCNDR: So free trade agreement management second group is the ensure air freight security and the third group that is only affecting European region is interested reporting and now you can see which processes out of all the customers management processes are affected by ERP template.
Gendek Katarzyna CZS SCNDR: So where we have this connection between the customs management system and your program?
Gendek Katarzyna CZS SCNDR: So that's four HANA.
Gendek Katarzyna CZS SCNDR: So in the topic of ensure customer compliance, we have 4 processes that are connected with US 4 HANA and that's are carry out importdeclaration export declaration.
Gendek Katarzyna CZS SCNDR: FDA.
Gendek Katarzyna CZS SCNDR: So free trade agreement and conduct, customs classification and this last one Customs conduct customs classification.
Gendek Katarzyna CZS SCNDR: Sorry will be mandatory in every plant, but we'll go through this at one of the later slides regarding ensure air freight security.
Gendek Katarzyna CZS SCNDR: This is completely out of the scope of the connection with S4 HANA, so we do not have to discuss that.
Gendek Katarzyna CZS SCNDR: And regarding Intrastat reporting from for EMEA, market monthly reporting for inbound and outbound is also affected by ERP.
Gendek Katarzyna CZS SCNDR: So it's being done by Fiori applications in as four HANA.
Gendek Katarzyna CZS SCNDR: Umm, now we are switching it to customs systems overview and this is what I told you at the beginning, not call it always MIC implementation but customs systems implementation and IC.
Gendek Katarzyna CZS SCNDR: It is in fact the strategic system of customs management will four models.
Gendek Katarzyna CZS SCNDR: So CCS, OCS, cast and ECM.
Gendek Katarzyna CZS SCNDR: But this is not the only Customs management system.
Gendek Katarzyna CZS SCNDR: So in MIC, which is the preferred system whenever it may be implemented, we have this four models, the most important for us is CS, which will be mandatory in every plant and every region, no matter if we are in Europe, Americas or Asia Pacific and CCS is the model from where the custom storage code will be.
Gendek Katarzyna CZS SCNDR: Me umm.
Gendek Katarzyna CZS SCNDR: Populated in As for HANA, we have also all there, so preference preferential the calculation and the origin cast which is for import export declaration and ACM for FDA.
Gendek Katarzyna CZS SCNDR: But regarding the other custom system in use, so as I said, the preferred preferred way is that all the mandatory way is that MIC.
Gendek Katarzyna CZS SCNDR: OCS, sorry, CS will be in every plant.
Gendek Katarzyna CZS SCNDR: So this is about the custom storage code visible in as four HANA.
Gendek Katarzyna CZS SCNDR: It will be always taken from ICS.
Gendek Katarzyna CZS SCNDR: Other models will depends on the on the plant and the region, because in some plants we will go with import export with MIC CUST.
Gendek Katarzyna CZS SCNDR: But for example, in Brazil for import export declaration we need to go with software. Uh. Due to government requirement in China instead of MIC CUST we will go with tasks.
Gendek Katarzyna CZS SCNDR: We just again dedicated for China due to government requirement and will be replacement of MIC CUST in there.
Gendek Katarzyna CZS SCNDR: But we have also to order custom system systems AB and ALTRUST ATC, which are like in my see and are running mostly in Germany.
Gendek Katarzyna CZS SCNDR: But in future will be replaced by MIC, so we've AEB and Atlas, they are right now and will be replaced by MIC in the future.
Gendek Katarzyna CZS SCNDR: So we will get rid of the systems, but with the first two software and tasks there will be no no replaced but MIC at the moment due to legal requirements or government requirement from Brazil and China.
Gendek Katarzyna CZS SCNDR: So currently you have this 55 systems in my seat soft soft way task AEB address two last.
Gendek Katarzyna CZS SCNDR: We hope that we'll get rid of them and they will be replaced completely by MIC in some kind in in some future and here you can see in detail how they're affecting the S4 HANA project.
Gendek Katarzyna CZS SCNDR: So on the right of the screen you can see the project scope.
Gendek Katarzyna CZS SCNDR: This is not UELP template project called.
Gendek Katarzyna CZS SCNDR: It's more like customs and department projects scope and with a color.
Gendek Katarzyna CZS SCNDR: Blue, you see blue and red, but red at the beginning.
Gendek Katarzyna CZS SCNDR: Now with color blue you can see which processes are affected by S4 HANA.
Gendek Katarzyna CZS SCNDR: Once again, we saw we saw this on the second slide already.
Gendek Katarzyna CZS SCNDR: Here's in different form, but here we have how exactly they are located in S4 HANA.
Gendek Katarzyna CZS SCNDR: So in this blue box in the middle of the screen you can see the ERP template.
Gendek Katarzyna CZS SCNDR: So As for Hannah, and we can see the let me maybe.
Gendek Katarzyna CZS SCNDR: Hold the laser in here.
Gendek Katarzyna CZS SCNDR: So we have the second topic like carry out importdeclaration and the first topic carry out export declaration and we can see here this whole box is as 4 HANA and how they are connected.
Gendek Katarzyna CZS SCNDR: So for the import export here you can see that we have the MIC CUST so as I explained in the previous screen but it can be replaced by software in it have to be replaced by software in Brazil.
Gendek Katarzyna CZS SCNDR: And yes, in China, but we also have some all their systems that will be replaced by WICF.
Gendek Katarzyna CZS SCNDR: But but I running right now.
Gendek Katarzyna CZS SCNDR: So we have a B2 models for important export and we have Atlas which is more manual system.
Gendek Katarzyna CZS SCNDR: Then we have the number 5, so conduct systems classification and this is here and my CCS.
Gendek Katarzyna CZS SCNDR: So as I said, this will be a mandatory model in MIC for every rollout plant because from this model we will get the customs tariff code in our S4 HANA and then additionally we have FTEI management which is MIC, OCS model and at the very end with different color.
Gendek Katarzyna CZS SCNDR: We have this interested reporting which affecting only European market, umm and this is not done by external application but it taken the data from MIC.
Gendek Katarzyna CZS SCNDR: So it's taken the data from MIC CCS about the customs tariff code and then in S4 HANA directly we have two theory application one is for inbound and one is for outbound intrastat report.
Gendek Katarzyna CZS SCNDR: So the report with all the shipments, with the details, with the Customs details that are happen within, you're up.
Gendek Katarzyna CZS SCNDR: So for example, from Germany to France, uh, we need to report the the activities to the government very important in the from the customers perspective is master data and this will be one of the cases during the UAT's that we will check if the proper master data is set in S4 HANA to ensure that the for example outbound printouts will contain all the informations that allow us to perform the custom sequence.
Gendek Katarzyna CZS SCNDR: If they go through, be sent to the third party effort country.
Gendek Katarzyna CZS SCNDR: So we have like a month here of the transactions mode of transport.
Gendek Katarzyna CZS SCNDR: We have two topics where customs management is responsible only these two so country of origin custom custom tariff code and additionally we have all the details from the invoice like description of the goods quantity value regarding value we need invoice value and statistical value as well.
Gendek Katarzyna CZS SCNDR: Normally on the invoice we should have only the invoice value, so the value for the price that we are selling to them to the customer.
Gendek Katarzyna CZS SCNDR: So before I will go to the next slide, I would like to ask you if you have already any questions regarding this setup of the customs management management systems.
Gendek Katarzyna CZS SCNDR: And I hope that if there will be any questions, umm I will try to to answer or I will ask Harald to to support in that.
Gendek Katarzyna CZS SCNDR: So do you have any any kind of questions regarding this this screenshot, this this slide, sorry.
Schulz Timo BRM SCNDR3: And I have one question and when this project will start well for which project this will start?
Gendek Katarzyna CZS SCNDR: It all already started so with 24.1 plans.
Gendek Katarzyna CZS SCNDR: We are coming with MICMICS will be in every every plant and not sure if everything will be done before you waiting.
Gendek Katarzyna CZS SCNDR: We start with Shirley, then we are going with Fenton and Chandra.
Gendek Katarzyna CZS SCNDR: Gun as well because we May is running regarding the software, but at least we should we try to make it still before UAT.
Gendek Katarzyna CZS SCNDR: Umm, but there is no specific like test case to be tested in Fenton and in turn chagan because they are not affected by interested report.
Gendek Katarzyna CZS SCNDR: So the only thing that will be done is to check if the customs tariff code has been transmitted from MIC.
Gendek Katarzyna CZS SCNDR: CCS, but it will be from 2041.
Schulz Timo BRM SCNDR3: And the the master data and the master data.
Schulz Timo BRM SCNDR3: They this is organized that they will and be available, or so we haven't do nothing for that.
Gendek Katarzyna CZS SCNDR: The master data have to be in the system.
Gendek Katarzyna CZS SCNDR: It's not the topic of the of the customs.
Gendek Katarzyna CZS SCNDR: We need to have the master data like weight of the goods or the value of the goods it is in the system.
Gendek Katarzyna CZS SCNDR: It must be in the system to, for example, even create the sales order without the prices in the system, we will not create the sales order.
Gendek Katarzyna CZS SCNDR: The only two topics from whole master data where customs is responsible is the country of origin and customs tariff codes.
Gendek Katarzyna CZS SCNDR: Only this door.
Gendek Katarzyna CZS SCNDR: Uh master data fields are under customers management responsibility.
Boes Harald FRD SCMC: Right
Hadeler Jens FRD SCNDR: And to underline this, just a remark here, the other data is already in scope of the data migration says it's nothing new.
Hadeler Jens FRD SCNDR: It is only important to understand here by all of us that these data have more importance than other data because it's printed, it will be printed on documents that are legally required.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So for customs authorities relevant and that means that data quality is more important here.
Gendek Katarzyna CZS SCNDR: OK, well, I see some questions from the audience here.
Hadeler Jens FRD SCNDR: Then we have the raised hand of Phillips.
Hadeler Jens FRD SCNDR: That was the first one.
Heitz Philip BRM SCNDR: Yeah.
Heitz Philip BRM SCNDR: One question regarding the master data.
Heitz Philip BRM SCNDR: So with this statistical value mentioned is.
Heitz Philip BRM SCNDR: Is it meant this?
Heitz Philip BRM SCNDR: Yeah, GRWR statistical value.
Heitz Philip BRM SCNDR: This in German we work Wang Vert because and yeah, yeah.
Heitz Philip BRM SCNDR: So they're in for Sandra gang.
Heitz Philip BRM SCNDR: We had a quite yeah discussions.
Heitz Philip BRM SCNDR: Who is responsible to maintain this value condition in the system?
Heitz Philip BRM SCNDR: Whether it would be purchasing or customs or global customs or local customs, and yeah, wasn't really clear.
Heitz Philip BRM SCNDR: Who?
Heitz Philip BRM SCNDR: Who knows what to maintain or who knows what to maintain in this value conditions and who should do it in the end, yeah.
Heitz Philip BRM SCNDR: A.
Heitz Philip BRM SCNDR: Is it a Customs task or?
Boes Harald FRD SCMC: No, I I mean it's not because I do not want to work on things but but normally normally there is a calculation about the price and there are a few components inside a price calculation where we anyway get out the right price for a a free trade calculation for example.
Boes Harald FRD SCMC: And the guns?
Boes Harald FRD SCMC: We got young sphere to just to just to explaining in German with this word is something that needs to come out of the calculation of the price at all and we just need to find out where to pick it.
Boes Harald FRD SCMC: OK.
Boes Harald FRD SCMC: Because we have some freight.
Boes Harald FRD SCMC: Uh, inside the country and some freight outside the country.
Boes Harald FRD SCMC: And somehow this is calculated anyway and we just need to find the connection point to to take out this price exactly.
Boes Harald FRD SCMC: And if we do not have it, then we need to find the certain factor to calculate with a certain, with a certain incoterm and and this needs to be figured out.
Boes Harald FRD SCMC: OK.
Boes Harald FRD SCMC: I will say it's a proach.
Boes Harald FRD SCMC: Customs can only explain what it must be, how it must be, and then we need to find a way to figure that out.
Heitz Philip BRM SCNDR: But Yahoo, who does this calculation in the end, so yeah.
Hadeler Jens FRD SCNDR: Philip, we had a meeting yesterday already.
Hadeler Jens FRD SCNDR: There is that is in discussion.
Hadeler Jens FRD SCNDR: It is not finally completed, so I made a note here and we will deep dive on that separately.
Boes Harald FRD SCMC: And because it will come back and each plant, OK, the same, the same requirement, at least for the 27 EU plans, it is absolutely necessary.
Boes Harald FRD SCMC: And when I look through exported goods here, we also need it.
Boes Harald FRD SCMC: So it's also the outbound directed to other countries outside the EU.
Boes Harald FRD SCMC: So I think we cannot get rid of it.
Boes Harald FRD SCMC: We need to clarify.
Heitz Philip BRM SCNDR: Yeah
Ott Carina SCW SCNDB: I just have a question because I see here in the ERP template the bonded warehouse and to my knowledge we don't have bonded warehouse processes in the template yet, not just want to be sure that we're not forgetting something or overseeing something.
Hadeler Jens FRD SCNDR: As there is a ticket on that to develop it, we will have it in the future.
Boes Harald FRD SCMC: And and let me tell you, the bonded and.
Hadeler Jens FRD SCNDR: It came from Wuhan.
Hadeler Jens FRD SCNDR: And is it's.
Hadeler Jens FRD SCNDR: It is relevant to provide a solution.
Hadeler Jens FRD SCNDR: I don't have the details and I guess we can discuss that afterwards when it is allocated and who and where, but definitively this requirement will come.
Ott Carina SCW SCNDB: Could you just send me the ticket?
Ott Carina SCW SCNDB: Because I'm not aware right now that would be awesome.
Ott Carina SCW SCNDB: Thank you.
Boes Harald FRD SCMC: And it's probably not, let me say, because of that bonded warehouse.
Boes Harald FRD SCMC: I think most inside the ERP needs to be done because we need to separate a consignment goods and we need to separate those goods which are not declared for free.
Boes Harald FRD SCMC: So collation in the Customs process so means under customs control.
Boes Harald FRD SCMC: And it's it's a mixture out of consignment and customs regulations just to explain it on this way.
Hadeler Jens FRD SCNDR: Yep.
Hadeler Jens FRD SCNDR: OK.
Okal Jan GNL SCNDR1: Ohh it's me.
Okal Jan GNL SCNDR1: Ohh, just a A of the curiosity.
Okal Jan GNL SCNDR1: The question of with the software needed for Brazil, will we have special requirement for Mexico or will Mexico be covered by the MIC CUST?
Boes Harald FRD SCMC: So what you need to know, generally we do not always have a direct connection to the authorities in every single country or every single plant we have that sometimes where we do the declaration digitalized by ourselves and sometimes we use brokers and so, so the software is a soft software from Thomson Reuters which is.
Boes Harald FRD SCMC: Especially Design for the Brazilian market.
Boes Harald FRD SCMC: Where MIC software actually is not able to to to fight OK and the same with TAS in China.
Boes Harald FRD SCMC: OK.
Boes Harald FRD SCMC: So probably we find another one or two pieces of software later while doing the rollout in some some other country.
Boes Harald FRD SCMC: But but but this software is directly interacting then with the authorities piece of software.
Boes Harald FRD SCMC: Have I picked it up right or have I not answered the question?
Okal Jan GNL SCNDR1: So I am.
Okal Jan GNL SCNDR1: Maybe Mexico will need another.
Okal Jan GNL SCNDR1: Another special case, like that's how I understand it.
Boes Harald FRD SCMC: Maybe we need to find out.
Boes Harald FRD SCMC: It's not necessarily the case, and it depends on if we use certain brokers to do that filing with the authorities and I will not say that I'm 100% sure that this will not come up.
Boes Harald FRD SCMC: But at the moment, at least not.
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: OK, good.
Hadeler Jens FRD SCNDR: Then we have two more raised hands, one from Uncle Scene.
Wittner Ann Christin FRD SCNDR3: But how do we double check that?
Wittner Ann Christin FRD SCNDR3: Or what do we need to take in account?
Boes Harald FRD SCMC: Mean the most in the most difficult thing for the international handling is of course, everything where we do not have a material number in the in in the ERP system because this could be chocolate, this could be coffee or this could be wine.
Boes Harald FRD SCMC: OK, under the same dummy or don't know how to say it and here we need to here we need to find a solution to report those those things too and then this is almost the most difficult one we had that also in in our three and we need to find a way to report those too and and here it's manual manual workload not needed and therefore we hate those kind of deliveries.
Goebel Thorsten DLN SCMC2: Yes, maybe one one thing additionally regarding this material numbers, yes, sorry, sorry for the delay, you know and so but I think you have done a a very good explanation.
Goebel Thorsten DLN SCMC2: Have it so far, but regarding this infrastructure issue, of course we need material numbers and we need physical physical items.
Goebel Thorsten DLN SCMC2: The rest is not important either for the for the statistical authorities and also not for the customs authority.
Goebel Thorsten DLN SCMC2: So far as I know, well, the next rollout, we've planned a plan in Italy, in Italy, the the interest tart includes also things like services and services of course to an also not have any material code, but in the background we are developing also here a solution.
Goebel Thorsten DLN SCMC2: So we have to face also the issue physical and in this case also non physical items or services we are paying for.
Goebel Thorsten DLN SCMC2: This is in this exception also relevant for the interest that issue.
Goebel Thorsten DLN SCMC2: Yeah this.
Wittner Ann Christin FRD SCNDR3: So software is not a problem because this is not physical.
Boes Harald FRD SCMC: Right.
Goebel Thorsten DLN SCMC2: Soft software it it software, software, test.
Goebel Thorsten DLN SCMC2: It's it's not a proper as as far as as as it is not stored on a CD or on a USB stick then it's physical.
Wittner Ann Christin FRD SCNDR3: OK. Thanks.
Hadeler Jens FRD SCNDR: Customs is a very complex and very specific topic, and I guess we will not be able to answer everything.
Hadeler Jens FRD SCNDR: What Harald and Torsten and the other colleagues developed over years, but what we should implement here if there are questions.
Hadeler Jens FRD SCNDR: Yeah, use the expert groups.
Hadeler Jens FRD SCNDR: Yeah, use them.
Hadeler Jens FRD SCNDR: Discuss about raising your questions in detail.
Hadeler Jens FRD SCNDR: Yeah, this is this meeting here today can only provide an overview and we thought this would be important and the number of questions.
Hadeler Jens FRD SCNDR: Reflect that this was necessary.
Goebel Thorsten DLN SCMC2: OK.
Hadeler Jens FRD SCNDR: So but one more question to me.
Song Zhumei SGH SCNDR2: Yes, thank you.
Song Zhumei SGH SCNDR2: My question is actually simple, just want to ask if this interface for MIC CCS module will be included in the UAT scope for Zhangjiagang.
Hadeler Jens FRD SCNDR: Answer is yes.
Song Zhumei SGH SCNDR2: Oh, OK then.
Song Zhumei SGH SCNDR2: Uh, any?
Song Zhumei SGH SCNDR2: Uh preparation?
Song Zhumei SGH SCNDR2: Uh should be done.
Song Zhumei SGH SCNDR2: Uh.
Song Zhumei SGH SCNDR2: From a local roll out team.
Hadeler Jens FRD SCNDR: Uh, we should have the data as soon as the interface is there.
Hadeler Jens FRD SCNDR: We should have some test data, not with that everyone, but with that one next week and then check if the functionality is running.
Hadeler Jens FRD SCNDR: The expectation it is will, but it needs to be validated.
Goebel Thorsten DLN SCMC2: Yeah.
Goebel Thorsten DLN SCMC2: Yes, yesterday I have received the news from you 107 that the connection between MIC test system and Satya one is already done and now it's all about test data is like the ones mentioned here.
Gendek Katarzyna CZS SCNDR: And very important remark in here is that together with Torsten, we are, we are working on the test case on the Shirley example.
Gendek Katarzyna CZS SCNDR: So how the step with the Customs verification should looks like once it will be finalized?
Gendek Katarzyna CZS SCNDR: I believe this week we have the last meeting, Thorsten, correct me if I'm wrong.
Gendek Katarzyna CZS SCNDR: Umm, once we will agree how the test step should look like.
Gendek Katarzyna CZS SCNDR: I will share this with all the 24.1 projects separately.
Song Zhumei SGH SCNDR2: Good.
Song Zhumei SGH SCNDR2: Actually, I'm just about to ask about the how to prepare the testing scripts perfect.
Gendek Katarzyna CZS SCNDR: There will be no separate test script for this.
Gendek Katarzyna CZS SCNDR: We will just put this under the standard standard test cases because this is only about verification on that of the data.
Goebel Thorsten DLN SCMC2: Exactly.
Goebel Thorsten DLN SCMC2: And I I don't know, we have a colleague in, in, in Sansha gang.
Goebel Thorsten DLN SCMC2: Maybe.
Goebel Thorsten DLN SCMC2: Maybe you know Liao Dongpo.
Goebel Thorsten DLN SCMC2: This is our key user and this is the key user and we will proceed.
Goebel Thorsten DLN SCMC2: But you are also invited if you are interested in can only put be be good if there are some some more people invited.
Schulz Timo BRM SCNDR3: Was responsible for Shirley.
Song Zhumei SGH SCNDR2: OK, so he's a currently working on this topic and also preparation for the testing.
Goebel Thorsten DLN SCMC2: Yeah, I've.
Goebel Thorsten DLN SCMC2: I've heard in the background another question who's responsible for Shirley?
Goebel Thorsten DLN SCMC2: That the yeah.
Goebel Thorsten DLN SCMC2: Yeah.
Goebel Thorsten DLN SCMC2: We have nominated 3 people over there.
Goebel Thorsten DLN SCMC2: I've I've never seen these these people, but maybe, you know, he's Berkeley then.
Goebel Thorsten DLN SCMC2: Then then John Spellacy and Thomas Wallace.
Goebel Thorsten DLN SCMC2: These are the the three people and normal and and uh member of the European team.
Goebel Thorsten DLN SCMC2: What?
Goebel Thorsten DLN SCMC2: What do this?
Goebel Thorsten DLN SCMC2: Anna Wozniak.
Goebel Thorsten DLN SCMC2: But she's on vacation until the 19th of August.
Goebel Thorsten DLN SCMC2: But, but it makes sense that there are people from the plant taking care about this issue.
Schulz Timo BRM SCNDR3: OK.
Schulz Timo BRM SCNDR3: Thank you.
Gendek Katarzyna CZS SCNDR: So now coming back only for a minute, our presentation for various last minute because we are out of time drastically already.
Gendek Katarzyna CZS SCNDR: So I just want to mention that in here in the presentation there is also the rasic created for the custom system systems integration with ERP and I would strongly recommend to review especially for the customs uh experts here and outbound ETA how will proceed with the integration of the custom system.
Gendek Katarzyna CZS SCNDR: You can use the link in here or you can review three slides in the presentation with split two operational, tactical and strategic topics where we decide what are the main activities in each area and who will be responsible, who will be accountable, who will be supporting some kind of activity, receive the information or will be the one with the veto rights.
Gendek Katarzyna CZS SCNDR: So I strongly recommend to review this this rasic because this will be our way of working.
Gendek Katarzyna CZS SCNDR: From now on, we already have the weekly meetings with Torsten and sometimes flies and will be only involving more, more people to be on the right track with all the plans.
Goebel Thorsten DLN SCMC2: OK.
Boes Harald FRD SCMC: One last comment from my side Katarzyna.
Boes Harald FRD SCMC: As soon as the the SAP project is done OK, you can join the customs family.
Boes Harald FRD SCMC: I mean you explained it so perfectly.
Boes Harald FRD SCMC: Thank you for that.
Gendek Katarzyna CZS SCNDR: Yes, do not hear that.
Gendek Katarzyna CZS SCNDR: Do not hear that.
Boes Harald FRD SCMC: Yes.
Boes Harald FRD SCMC: Yes, uh, we find a way.
Boes Harald FRD SCMC: No, no, no.
Boes Harald FRD SCMC: No, no, no, no, no.
Goebel Thorsten DLN SCMC2: Orlando cool.
Hadeler Jens FRD SCNDR: OK, cool.
Hadeler Jens FRD SCNDR: Good.
Hadeler Jens FRD SCNDR: So then, as Catarina said, we are a little bit behind timing, but we have a buffer for the Q&A session.
Hadeler Jens FRD SCNDR: So many questions have already been raised, but anyway, we have the European, the Asia Pacific and the Americas view.
Martos Francisco Javier VTR SCNDR3: OK.
Martos Francisco Javier VTR SCNDR3: Thank you.
Martos Francisco Javier VTR SCNDR3: I will share the do you see my screen?
Martos Francisco Javier VTR SCNDR3: Yes, Sir.
Martos Francisco Javier VTR SCNDR3: OK.
Martos Francisco Javier VTR SCNDR3: Then I will start.
Martos Francisco Javier VTR SCNDR3: OK, good.
Martos Francisco Javier VTR SCNDR3: At the moment, some of us, some of the DTA's, are preparing the UAT for Shirley.
Martos Francisco Javier VTR SCNDR3: So means that we are working on the Q test.
Martos Francisco Javier VTR SCNDR3: We added Q test and we reviewed the queue test with the queue users and we have a scheduled different meetings between the key users and make kind of training sessions in that set E1 and the test runs.
Martos Francisco Javier VTR SCNDR3: We try to make test runs in the thread E1 also and since yesterday we started or start with a Precheck and right dry runs in the set T1.
Martos Francisco Javier VTR SCNDR3: This is the test environment where where we will work or what will make the unit test with with the key users starting the 1st of August.
Martos Francisco Javier VTR SCNDR3: At the moment we only checking material master because there are missing data like scheduling agreements.
Martos Francisco Javier VTR SCNDR3: So apply US customers and so on.
Martos Francisco Javier VTR SCNDR3: Hopefully we will have it this week because the idea is really if it's possible to make in 71 some test runs to see if really everything is working and then hand over to the key user and next week.
Martos Francisco Javier VTR SCNDR3: But at the moment we don't start.
Martos Francisco Javier VTR SCNDR3: So I would say this is a critical point, but we are working on it.
Martos Francisco Javier VTR SCNDR3: OK, I'm question here to the due test or if not I will continue.
Martos Francisco Javier VTR SCNDR3: Good status localization concept here.
Martos Francisco Javier VTR SCNDR3: I resume a lot because I have only few minutes and of the localization concept bouzonville and Jablonec.
Martos Francisco Javier VTR SCNDR3: We are here.
Martos Francisco Javier VTR SCNDR3: We'll have a meeting with the domain.
Martos Francisco Javier VTR SCNDR3: All empty management and Woodinville.
Martos Francisco Javier VTR SCNDR3: I AMR connection to SAP via synaos.
Martos Francisco Javier VTR SCNDR3: If you, umm structure we work according to the you know settings and process design for copec this I would say are the most important.
Martos Francisco Javier VTR SCNDR3: And points, but I add to this presentation also the one page I have somebody wants to go through and I see a little bit of everything.
Martos Francisco Javier VTR SCNDR3: So demo of we are still working on the demo of segment of the business partner to the warehouse is missing no EVM in Bouzonville.
Martos Francisco Javier VTR SCNDR3: Sorry, no VM objects have been migrated yet.
Martos Francisco Javier VTR SCNDR3: Just migration 2 objects and fields need to be checked and redefined.
Martos Francisco Javier VTR SCNDR3: We are working on data cleansing and corrections at the moment and also with the packaging instruction.
Martos Francisco Javier VTR SCNDR3: Yeah, we are reworking.
Martos Francisco Javier VTR SCNDR3: Yeah, we we work then a little.
Martos Francisco Javier VTR SCNDR3: OK, Jablonec similar situation.
Martos Francisco Javier VTR SCNDR3: Material Master is available for valuation there.
Martos Francisco Javier VTR SCNDR3: The colleagues are working on business part dot, dot, dot, packaging, etcetera, but it's still not being migrated and here again the packaging instruction also other objects has to be redefined.
Martos Francisco Javier VTR SCNDR3: So it's all from mean.
Martos Francisco Javier VTR SCNDR3: The most important topics from EMEA region.
Martos Francisco Javier VTR SCNDR3: Any questions or?
Hadeler Jens FRD SCNDR: One remark from my side.
Hadeler Jens FRD SCNDR: So what is relevant here for everybody?
Hadeler Jens FRD SCNDR: For all regions, is the queue test topic. Yeah.
Hadeler Jens FRD SCNDR: So Shirley is our blueprint.
Hadeler Jens FRD SCNDR: That means we test the integration test conduction via Q test.
Hadeler Jens FRD SCNDR: Why only we do it only in Shirley at the moment.
Hadeler Jens FRD SCNDR: To learn more, to bring, make more people familiar with the procedures.
Hadeler Jens FRD SCNDR: But for all projects running in release 25.2 or rollout Circle 23.2.
Hadeler Jens FRD SCNDR: This will become relevant, so the project set are already started, like echo like Fenton Fowlerville and uh Zhangjiagang will continue as they have started, but the other projects will goes the Shirley Way.
Martos Francisco Javier VTR SCNDR3: OK.
Hadeler Jens FRD SCNDR: OK, so great pilot work here and let's see how it develops.
Hadeler Jens FRD SCNDR: Could any any further questions, wishes, remarks.
Hadeler Jens FRD SCNDR: If there is not the case, umm, then I would like to hand over to Yanyan.
Guo Yanyan SGH SCNDR2: Hello.
Guo Yanyan SGH SCNDR2: So are we ensure my slide?
Guo Yanyan SGH SCNDR2: OK, so I have a really a very brief slide from my side from a from Asia Pacific.
Guo Yanyan SGH SCNDR2: So firstly as you can see that we have an update from our UH-2 rollout projects here for Mohan project and we had a beginning July decision that Yuhong project stopped.
Guo Yanyan SGH SCNDR2: You stop already stopped because they have a relocation activities running in parallel in 2025.
Guo Yanyan SGH SCNDR2: So right now we have only this 10 second project running now.
Guo Yanyan SGH SCNDR2: So right now we are following the project plan to work on the preparation for the UAT.
Guo Yanyan SGH SCNDR2: So from DTA side I think we are keeping the on track with all the tasks and what can I what I can summarize as high like highlighting points actually from our region would be that this one key point of a master data topic.
Guo Yanyan SGH SCNDR2: So I what I feel that we have done better then the other area will be that we had really had worked out a good collaboration mode with our project team and members and also plant keysers to on this material master data topic.
Guo Yanyan SGH SCNDR2: So we have to order though main functions are fully aligned to ensure that data quality and on the other side for the Ewa master data we also have been working out methodology team in order to prepare this Edu plan related master data in a very efficient way.
Guo Yanyan SGH SCNDR2: So especially for the production supply related master data, so as soon as this is done and then for the next time for the go live, we will really have much easier work in regarding the master data preparation and with good quality.
Guo Yanyan SGH SCNDR2: And the other point I would like to say is that.
Guo Yanyan SGH SCNDR2: Yeah, we I think it's also a good point that we could really align adjust quickly ohan DTA resources to support on the gun project and at the same time, it will, we had some, we had a lot of work increased actually I would say and some of the IT DTS previously working on the project.
Guo Yanyan SGH SCNDR2: They also have reduced their capacity, not only because of there are some reasons behind, but also because the workload increased for also for IT side.
Guo Yanyan SGH SCNDR2: So we could really take the chance to bring our own handed a resources immediately and to the Junjun project.
Guo Yanyan SGH SCNDR2: And I can see that our PTA colleagues from Mohan, they are really having the very quick hands on supporting the Zhangjiagang project.
Guo Yanyan SGH SCNDR2: For example, during the Keesler trainings and the working together to on the test cases preparation.
Guo Yanyan SGH SCNDR2: So it's really quite a smooth transfer or adjustment of their of their capacity to the to the Zhangjiagang project and which I think is really good.
Guo Yanyan SGH SCNDR2: And because we first one is that we are adapting quickly and on the other hand and we really need those resources for the transaction project because it's as a big, big and complex location and say so, the risking risk risks.
Guo Yanyan SGH SCNDR2: I would mention two major points, which is really UAT and go live critical the the outbound EWM outbound topic.
Guo Yanyan SGH SCNDR2: I think there were already a lot of discussion about this, so here we are still having some delays.
Guo Yanyan SGH SCNDR2: Some are not yet, so testing is that Qin but not yet in that T1 or that E1 even and some are not even tested in that queue one.
Guo Yanyan SGH SCNDR2: So we hope that this could be really resolved as fast as possible, but on the other hand, there is another topic which I see more risk in there because of the ball.
Guo Yanyan SGH SCNDR2: Manuel Tier number guidance is still not clear for us and we have 500 bones there out there and none of them are in the system yet and we will have the UAT plan for next Monday starting from next Monday.
Guo Yanyan SGH SCNDR2: So I cannot really imagine how this could be achieved this task within one week.
Guo Yanyan SGH SCNDR2: So we can start our 80 on time, OK.
Guo Yanyan SGH SCNDR2: So some key highlights and risky points from our AP side and the one last a slight this showing our DTA team as you can see we have one new colleague joining us from Doom, Vijay, he's from Division T transferred to our DTA teams and 10s of June.
Guo Yanyan SGH SCNDR2: So we, Jay will be focusing on the warehousing topic and also the outbound uh topic from our AP team.
Guo Yanyan SGH SCNDR2: OK, that's all share from my side.
Guo Yanyan SGH SCNDR2: Do we have?
Guo Yanyan SGH SCNDR2: Do I have some questions from?
Zeng Lingpeng SGH COOA: Umm.
Zeng Lingpeng SGH COOA: Maybe, maybe, maybe I could.
Zeng Lingpeng SGH COOA: I I want to make some, yeah comments not not not question to the Yanyan by let's say making some more explanation about this risk and highlight here and because we see that from this summary that Yang Yang is sharing two point low on one side mature master preparation very good it's a corporation and they four let's say good let's say yeah data preparation but on other side we see that when risk but the bomb and mathematic guideline is still not here while we see a a father knowing from the project that they this as you know.
Zeng Lingpeng SGH COOA: Finished that saying 2 number and forms was still majority.
Zeng Lingpeng SGH COOA: Not really as clean, though.
Zeng Lingpeng SGH COOA: They preparation of mass data were based on existing queued the Martin number and bombs.
Zeng Lingpeng SGH COOA: That's for sure.
Zeng Lingpeng SGH COOA: Well, let's say bring big challenge into the project because Andy, it's so far the preparation was official with material number and bombs was far below the behind the schedule.
Zeng Lingpeng SGH COOA: And I'm not sure if this, although all the question to our colleague Domain asked from from European.
Zeng Lingpeng SGH COOA: I'm not sure for for Shirley or for colleague from other America if they have a similar problem with say, uh with Andy Department.
Zeng Lingpeng SGH COOA: Now we have uh like.
Zeng Lingpeng SGH COOA: Is it specific?
Zeng Lingpeng SGH COOA: How it's going there?
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So the the order to cache challenge is the same across all projects.
Hadeler Jens FRD SCNDR: Not that much relevant for Shirley because of the size of the plant.
Hadeler Jens FRD SCNDR: Yeah, but, and they have a simplified approach, small factory approach, but the BOM guidance is the same critical for Fenton and foul level.
Hadeler Jens FRD SCNDR: Yeah, as it is for Zhangjiagang, for Shirley, we have of the 14 use cases of the MBOM concept, we have only one and with only a few material numbers that has been adjusted manually.
Hadeler Jens FRD SCNDR: So that is not a big deal and and in engineering cohelio the problem is there, but the time pressure is not that big as it is infant and four level and in Junjun.
Hadeler Jens FRD SCNDR: But don't feel alone with that problem.
Hadeler Jens FRD SCNDR: The problem is exactly the same, even if Zhangjiagang plant is bigger the size.
Zeng Lingpeng SGH COOA: OK, got it.
Zeng Lingpeng SGH COOA: Thank you. Yeah.
Hadeler Jens FRD SCNDR: Or met from your side.
Hadeler Jens FRD SCNDR: Any additional comments or somebody else from the America's team?
Ashmore Matt NRT SCNDR1: Not from my side, Jens.
Hadeler Jens FRD SCNDR: OK, good.
Hadeler Jens FRD SCNDR: Any more questions regarding Yanyan's presentation?
Hadeler Jens FRD SCNDR: The status update from Asia Pacific region.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So last comment from my side that what you did, APEC team not only Yanyan but led by Yanyan.
Hadeler Jens FRD SCNDR: Regarding this data consistency, that is really really a good contributor and that has the potential to become a blueprint for our future project work.
Hadeler Jens FRD SCNDR: This is really, really good.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: And especially in combination with that, what's the America's team is doing regarding a check of system consistency data?
Hadeler Jens FRD SCNDR: This will bring us really a big step forward.
Hadeler Jens FRD SCNDR: OK, good.
Hadeler Jens FRD SCNDR: So then yeah, exactly.
Hadeler Jens FRD SCNDR: I'm always looking positively forward. Yeah.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: Good morning everyone.
Ashmore Matt NRT SCNDR1: I apologize for not turning my camera on, but the the meeting started at 5:30 this morning and and it's a little pre caffeine so I'm sure no one wants to see me.
Ashmore Matt NRT SCNDR1: I will share my screen really quick.
Ashmore Matt NRT SCNDR1: Go over some quick highlights, very brief as well.
Ashmore Matt NRT SCNDR1: We're in the finalization phase of the localization concept from martial.
Ashmore Matt NRT SCNDR1: There's an echo, I'm not sure.
Hadeler Jens FRD SCNDR: Can everybody switch off the microphone?
Boes Harald FRD SCMC: Was again me.
Boes Harald FRD SCMC: Sorry for that.
Ashmore Matt NRT SCNDR1: And no problem.
Ashmore Matt NRT SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: So again, we're in the finalization phases of the localization concept for Marshall in the Limeira project.
Ashmore Matt NRT SCNDR1: The deadline is the end of calendar week 30, so next week.
Ashmore Matt NRT SCNDR1: As Yanyan alluded to, she did a great proactive job in the data correction or the day of validation or preparation.
Ashmore Matt NRT SCNDR1: But we in the Americas did not, to be honest.
Ashmore Matt NRT SCNDR1: And now we're doing a more reactive approach.
Ashmore Matt NRT SCNDR1: We have developed an automated data validation tool.
Ashmore Matt NRT SCNDR1: Essentially, a colleague and our team has created a Python or he's developed automated solution using Python And some other umm inputs from SAP to automate the validation and.
Ashmore Matt NRT SCNDR1: Out the output of that validation tool is to give us the corrections that are needed.
Ashmore Matt NRT SCNDR1: So we're working with the migration scope master and the DMF team to implement those changes before the before the UAT starting on site for us August 5th.
Ashmore Matt NRT SCNDR1: Umm, we are in the UAT preparation for ECHO and Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: Not many highlights from the Americas, but more risk.
Ashmore Matt NRT SCNDR1: Yeah, we have the UAT risk to EWM outbound topics that we were already discussed by cashing material master data, which we hope to mitigate with the automated validation tool we have the subcontracting and bailment process there in ECHO and of course the open customs tickets which were already discussed before.
Ashmore Matt NRT SCNDR1: One additional risk that we have in fit and fowlerville is the relocation of a plant in Ohio 2 Fenton.
Ashmore Matt NRT SCNDR1: It's Finlay that plant is in the progress or in the in progress of being closed and be allocated to Fenton.
Ashmore Matt NRT SCNDR1: So the line moves start as early as September.
Ashmore Matt NRT SCNDR1: Umm, they will.
Ashmore Matt NRT SCNDR1: They have a a transition phase that they're planned, but we don't know what exact impact that has on the go live for Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: Of course, one topic is the warehouse structure.
Ashmore Matt NRT SCNDR1: It looks like Findlay or Fenton will do completely away with this internal warehouse and go externally, but a lot of questions are still not yet answered, but that is is a huge risk for the go live in Benton and Fowlerville.
Ashmore Matt NRT SCNDR1: Any questions from from your side?
Hadeler Jens FRD SCNDR: So at least a risk for the for one one of the two plants.
Hadeler Jens FRD SCNDR: That means for Fenton, yeah.
Hadeler Jens FRD SCNDR: If it is effecting foul level as well, it depends on the on the warehouse usage in Howell.
Hadeler Jens FRD SCNDR: What we investigate later today, right?
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So that it's a little bit foggy at the moment.
Hadeler Jens FRD SCNDR: So the information is pretty fresh from the press.
Hadeler Jens FRD SCNDR: It has been communicated, meanwhile, officially so.
Hadeler Jens FRD SCNDR: We are checking that, but what is the important message here?
Hadeler Jens FRD SCNDR: If we have such events that go in conflict with the roll out plan, we need to know that as early as possible.
Hadeler Jens FRD SCNDR: So colleagues here from division U and from Division C If you know, or if you hear rumors that there is something, yeah, we don't need to go and escalation, but we need to talk about that.
Hadeler Jens FRD SCNDR: And even if it's only verbally, we need to know that there is something that might affect the program so that we can as early as possible, adjust to it and not as in this case, everything is already running.
Hadeler Jens FRD SCNDR: The first machines are nearly loaded on the truck and then the information comes that is too late.
Hadeler Jens FRD SCNDR: Yes.
Hadeler Jens FRD SCNDR: So that is really a bad example how it should not run and even if sometimes it's not possible to write something down but a verbal communication.
Hadeler Jens FRD SCNDR: At least would be helpful to identify risks and to manage them better to avoid such a situation like we have here at this point of time.
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: Anymore questions regarding Matt's presentation here maths update.
Hadeler Jens FRD SCNDR: So if that is not the case, then I would like to open general question and answer session even if we only have 17 minutes but better 17 minutes than 0 minutes.
Hadeler Jens FRD SCNDR: So if there are general questions what have not yet been who, where as the peep coming from?
Hadeler Jens FRD SCNDR: OK, if there are general questions what we can discuss here or where we can give answers to please raise your hand.
Hadeler Jens FRD SCNDR: This is for to do that.
Hadeler Jens FRD SCNDR: So there was one raised hand from Yanyan.
Guo Yanyan SGH SCNDR2: Yes, yes.
Guo Yanyan SGH SCNDR2: Can you go back to the slides?
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: So just a moment.
Guo Yanyan SGH SCNDR2: Yeah.
Guo Yanyan SGH SCNDR2: The slight Devon #9 #9.
Hadeler Jens FRD SCNDR: Umm I have it this one.
Hadeler Jens FRD SCNDR: You mean right?
Guo Yanyan SGH SCNDR2: Yes, about the future of picture.
Guo Yanyan SGH SCNDR2: And yeah, I I maybe we don't have time to discuss more, but I want to have the understanding of this feature, how this future is. Looks for me.
Guo Yanyan SGH SCNDR2: Or we were going to have a new allocation of responsibilities to the DTA's to fit to these future mode of now of rollout that we we in domain S We will split the domains related project work into three part and and also the EWM the the warehousing part because warehousing part previously as I understood is belonging to P2B.
Guo Yanyan SGH SCNDR2: So all these warehousing task defining warehouse structure or the process are also in the the the the P2P but then it is then a different understanding of different scope now than later.
Guo Yanyan SGH SCNDR2: All we are free to allocate different sub work streams to different to the Dtas within one team.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So in in general this is a journey.
Hadeler Jens FRD SCNDR: Yeah, and this is not effective or will not be come effective by tomorrow or by next week.
Hadeler Jens FRD SCNDR: So first topic is we continue working.
Hadeler Jens FRD SCNDR: Yes, it is written on the left side.
Hadeler Jens FRD SCNDR: So as today we have the inbound the warehousing, production outbound and purchasing or sourcing and this will not going to change in the near future.
Hadeler Jens FRD SCNDR: But as you all learned or as we all learned during the integration test, and it will happen the same way in the user acceptance test, we all need to think more in end to end scenarios.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So it doesn't help if we have the best outbound logistics process.
Hadeler Jens FRD SCNDR: If the printouts and the container management and performance management won't work, so it needs to fit together.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: And we all need to learn more thinking and end to end scenarios and we will need to evaluate for the next year's projects if this will become relevant for the team setup already or if we need more time.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So therefore he is no date mentioned.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So, but the journey goes in this direction.
Hadeler Jens FRD SCNDR: We all need to think more.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So it doesn't help if a if if a person thinks only in Customs process and the other one in sales process and the other one in warehousing process, we need one order to cash process at the end that works and this is where I want to motivate you to think outside the box to think more left and more right and think in end to end scenarios.
Hadeler Jens FRD SCNDR: That is what this slide should show.
Hadeler Jens FRD SCNDR: There is no written date by when it will become affected effective.
Hadeler Jens FRD SCNDR: There is no a documented way of working hey person a needs to train this in that by then to make it happen.
Hadeler Jens FRD SCNDR: But I want to motivate you.
Hadeler Jens FRD SCNDR: To actively go this way, to look left to look right and think in end to end scenarios.
Hadeler Jens FRD SCNDR: Does it answer your question Yanyan?
Guo Yanyan SGH SCNDR2: Yeah, I think this general direction is OK for me, but I think we need to invest more time to talk a little bit about the details.
Guo Yanyan SGH SCNDR2: This is how we want to allocate ourself, but at the same time I think we also have to think about how the business, how the plant is really their organization, umm situation, because if there it could happen that maybe we we to DTA's responsible for different end to end process they are running into the same queues or some examples.
Hadeler Jens FRD SCNDR: Right.
Hadeler Jens FRD SCNDR: Absolutely right.
Hadeler Jens FRD SCNDR: So this is only the beginning of the long journey.
Guo Yanyan SGH SCNDR2: OK, good.
Guo Yanyan SGH SCNDR2: Thank you.
Hadeler Jens FRD SCNDR: Anymore topics?
Hadeler Jens FRD SCNDR: Thanks.
Hadeler Jens FRD SCNDR: Or questions or wishes.
Hadeler Jens FRD SCNDR: What topics should be discussed here in the group?
Hadeler Jens FRD SCNDR: Not everybody at the same time.
Boes Harald FRD SCMC: The stock has a bit more about Customs.
Boes Harald FRD SCMC: Yens.
Boes Harald FRD SCMC: Could you explain me how it works?
Hadeler Jens FRD SCNDR: What in in in only two minutes I cannot explain how customs works.
Hadeler Jens FRD SCNDR: That's too complex.
Boes Harald FRD SCMC: This is what my what my boss always asking me explain me in 2 minutes.
Boes Harald FRD SCMC: Right.
Boes Harald FRD SCMC: Uh, just wanna be the one who fills up the gap.
Boes Harald FRD SCMC: Absolutely.
Hadeler Jens FRD SCNDR: So question to the colleagues here from division you and Division C, are there any wishes, remarks or questions from your side?
Teggerdine David NRT COOP: Nothing that can be answered in 2 minutes.
Hadeler Jens FRD SCNDR: Yeah, we have 10.
Hadeler Jens FRD SCNDR: We still have 10 minutes.
Teggerdine David NRT COOP: Now that's just joking.
Hadeler Jens FRD SCNDR: OK.
Burkard Manuel SCW FIBC: I I have each one.
Teggerdine David NRT COOP: Yeah.
Burkard Manuel SCW FIBC: I've indeed one question.
Burkard Manuel SCW FIBC: So some of the divisional process experts, they, they come into the project and are very short notice and without training for special software like EWM mainly.
Burkard Manuel SCW FIBC: So the question is, in order to to keep track with the, let's say developments that we have internally, is there also some kind of training for scene for the divisional process experts?
Hadeler Jens FRD SCNDR: Everybody, everything, clear statement.
Hadeler Jens FRD SCNDR: Everything what is available for DTA S is the same available for divisional process experts.
Hadeler Jens FRD SCNDR: There is no barrier, no separation between.
Hadeler Jens FRD SCNDR: So we already organized to invite everybody to the weekly expert group meetings to the process trainings.
Hadeler Jens FRD SCNDR: Some divisional process experts are already involved in the EWM trainings and everything.
Hadeler Jens FRD SCNDR: What will be done is open for everybody.
Hadeler Jens FRD SCNDR: So there is no roadblock and no hesitation.
Burkard Manuel SCW FIBC: OK, whom do I need to contact in order to get on such the distribution list like expert sessions and all that stuff?
Hadeler Jens FRD SCNDR: You can easily take it and hear the process.
Hadeler Jens FRD SCNDR: Trainers are all here in the call, so if Manuel wants to be involved then make a note here and please forward the invitation.
Hadeler Jens FRD SCNDR: Bernard. Philip.
Hadeler Jens FRD SCNDR: Kasia Yang and the other Yan.
Burkard Manuel SCW FIBC: Now I'm mainly interested in warehousing because there is my my supportive task, so would appreciate if I would receive the invitations.
Burkard Manuel SCW FIBC: Thank you, Hans.
Meschede Jan SCW SCNDR: Yeah, this should not be a problem, Manuel.
Hadeler Jens FRD SCNDR: OK, any more topic?
Teggerdine David NRT COOP: Matt, you have a question for me?
Ashmore Matt NRT SCNDR1: I'll call you later, David.
Teggerdine David NRT COOP: OK.
Hadeler Jens FRD SCNDR: So anymore topic, there is no stupid questions.
Hadeler Jens FRD SCNDR: There can only be stupid answers.
Zeng Lingpeng SGH COOA: Umm, no, I don't have it.
Hadeler Jens FRD SCNDR: Cool.
Hadeler Jens FRD SCNDR: OK.
Hadeler Jens FRD SCNDR: If no more questions are there, then I have 7 minutes to take some lunch. Cool.
Zeng Lingpeng SGH COOA: Thank you.
Zeng Lingpeng SGH COOA: I can leave office 7 minutes earlier.
Zeng Lingpeng SGH COOA: Goodbye.
Goebel Thorsten DLN SCMC2: Bye.
Hadeler Jens FRD SCNDR: Have a great rest of the day.
Zhao Lisa ZHN SCNDR2: Bye.
