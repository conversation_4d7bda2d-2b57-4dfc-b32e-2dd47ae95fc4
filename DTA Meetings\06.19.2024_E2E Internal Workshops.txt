<PERSON><PERSON> Matt NRT SCNDR1: We will go with the warehouse steps confirmation of the.
<PERSON><PERSON> Matt NRT SCNDR1: Umm.
<PERSON><PERSON> Matt NRT SCNDR1: Warehouse task then <PERSON><PERSON> call, mentioned that he would be a few minutes, but on.
<PERSON><PERSON> Matt NRT SCNDR1: He will join in a in a few minutes, then we will do the.
<PERSON><PERSON> Matt NRT SCNDR1: Production logistics side, if he's not here for sure, then then <PERSON> can take it.
<PERSON>more Matt NRT SCNDR1: But I had align with him, so hopefully he joins in just a few minutes and yeah.
<PERSON><PERSON> Matt NRT SCNDR1: Then we'll do production.
<PERSON><PERSON> Matt NRT SCNDR1: Ohh hey <PERSON>.
<PERSON>more Matt NRT SCNDR1: And then we'll go into production supply.
<PERSON><PERSON> Matt NRT SCNDR1: So <PERSON>, if you would like to start?
<PERSON><PERSON><PERSON> Angel MNR SCNDR1: Yeah, sure.
Se<PERSON>ra Jose Angel MNR SCNDR1: Well, what I want to to do is to to do it in in the in the system.
<PERSON><PERSON><PERSON> MNR SCNDR1: So maybe I can take something from.
<PERSON><PERSON><PERSON> <PERSON> MNR SCNDR1: SCMS1 or something like that?
<PERSON><PERSON><PERSON> Angel MNR SCNDR1: Because yeah.
<PERSON><PERSON> Matt NRT SCNDR1: Yeah, use ZSSM1 and you can confirm the warehouse test that was created by <PERSON>.
<PERSON>more Matt NRT SCNDR1: In the last last meeting.
<PERSON><PERSON><PERSON> Angel MNR SCNDR1: Yeah, I'm going to open that.
Segura <PERSON> Angel MNR SCNDR1: Yes, I was really there.
Segura Jose Angel MNR SCNDR1: Want to share?
Segura Jose Angel MNR SCNDR1: Yes.
Segura Jose Angel MNR SCNDR1: So I'm here, this is the.
Segura Jose Angel MNR SCNDR1: What has that so in in which one warehouse we were the last time we were in finance.
Segura Jose Angel MNR SCNDR1: So, well, do you really see this, this and warehouse monitor here we we is where we see all the movements in in for for the warehouse.
Segura Jose Angel MNR SCNDR1: So right now I'm going to the warehouse things and in the monitor SAP.
Segura Jose Angel MNR SCNDR1: So there are some ways to to look into for some specific.
Segura Jose Angel MNR SCNDR1: A warehouse task.
Segura Jose Angel MNR SCNDR1: And the third one could be if you only have the the number of the warehouse task, you can go to to the document and double click on warehouse task and if you have the the.
Segura Jose Angel MNR SCNDR1: The number of the warehouse you could put it there if not like in this case, and it's supposed to don't have too many warehouse that's here.
Segura Jose Angel MNR SCNDR1: So we can do the query open, so I'm going to to review this.
Segura Jose Angel MNR SCNDR1: Ohhh yes, I don't think that going to find too many but I think yeah sorry.
Okal Jan GNL SCNDR1: Yeah, you didn't unclick the finished ones and confirmed ones and cancelled ones.
Segura Jose Angel MNR SCNDR1: Uh, yes, yes, you're right.
Segura Jose Angel MNR SCNDR1: So.
Segura Jose Angel MNR SCNDR1: Maybe we just need the open right?
Okal Jan GNL SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: Umm.
Segura Jose Angel MNR SCNDR1: It's a lot also.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: Uh, Renee, can you put the the inbound document and the?
Segura Jose Angel MNR SCNDR1: Yeah, I mean it would would be, yes, I think I think we're in the in the chat for here, right.
Segura Jose Angel MNR SCNDR1: The pattern.
Ashmore Matt NRT SCNDR1: I don't see it, but.
Westrup Renee MNR SCNDR1: Please give me a second.
Segura Jose Angel MNR SCNDR1: Thank you.
Westrup Renee MNR SCNDR1: Uh, they mount delivery, right?
Westrup Renee MNR SCNDR1: So let me let me send it to you.
Westrup Renee MNR SCNDR1: This is a inbound delivery and the material document.
Westrup Renee MNR SCNDR1: I don't know if it is helpful for you.
Segura Jose Angel MNR SCNDR1: I'm gonna take the inbound delivery and put it here.
Segura Jose Angel MNR SCNDR1: LEI delivery and process.
Segura Jose Angel MNR SCNDR1: No.
Ashmore Matt NRT SCNDR1: Yeah, it's been closed.
Segura Jose Angel MNR SCNDR1: Let's close.
Segura Jose Angel MNR SCNDR1: And my item.
Ashmore Matt NRT SCNDR1: Can you scroll back to the?
Westrup Renee MNR SCNDR1: Umm I have another one.
Segura Jose Angel MNR SCNDR1: OK, let me back down.
Westrup Renee MNR SCNDR1: I'm. I'm sorry.
Westrup Renee MNR SCNDR1: That's from yesterday.
Segura Jose Angel MNR SCNDR1: Ohh.
Segura Jose Angel MNR SCNDR1: There's one.
Segura Jose Angel MNR SCNDR1: OK, let's see.
Segura Jose Angel MNR SCNDR1: And process.
Segura Jose Angel MNR SCNDR1: Oop.
Segura Jose Angel MNR SCNDR1: OK, so let's happen something.
Segura Jose Angel MNR SCNDR1: So, OK, umm, going to try to look one that is open here just to.
Westrup Renee MNR SCNDR1: I do you want me to create one really fast?
Segura Jose Angel MNR SCNDR1: You're confirm that would be great.
Segura Jose Angel MNR SCNDR1: So thank you.
Westrup Renee MNR SCNDR1: This is a little bit slow.
Segura Jose Angel MNR SCNDR1: Mm-hmm.
Segura Jose Angel MNR SCNDR1: And started.
Segura Jose Angel MNR SCNDR1: 21.
Segura Jose Angel MNR SCNDR1: Yeah.
Segura Jose Angel MNR SCNDR1: Created.
Segura Jose Angel MNR SCNDR1: Here this one for example is from.
Segura Jose Angel MNR SCNDR1: This.
Segura Jose Angel MNR SCNDR1: OK, let's see if this one could help.
Segura Jose Angel MNR SCNDR1: The only time man no.
Segura Jose Angel MNR SCNDR1: Work also dated transit.
Segura Jose Angel MNR SCNDR1: So Yasmin, maybe I can use this one?
Segura Jose Angel MNR SCNDR1: Is this not receive it?
Segura Jose Angel MNR SCNDR1: I didn't.
Segura Jose Angel MNR SCNDR1: As you can see here in the.
Segura Jose Angel MNR SCNDR1: In the status, it's not the shifts nor started.
Segura Jose Angel MNR SCNDR1: No.
Segura Jose Angel MNR SCNDR1: Put away.
Segura Jose Angel MNR SCNDR1: It's in transit.
Segura Jose Angel MNR SCNDR1: Maybe I can use this one, just let me see the details here.
Segura Jose Angel MNR SCNDR1: Just FINS.
Segura Jose Angel MNR SCNDR1: Inbound delivery.
Westrup Renee MNR SCNDR1: I I have a new one but I I miss to put the batch number from the supplier so I will do that really fast.
Westrup Renee MNR SCNDR1: Now it's saved.
Okal Jan GNL SCNDR1: Ohh may I have a question.
Segura Jose Angel MNR SCNDR1: Same one.
Westrup Renee MNR SCNDR1: Uh, I I did it in six Q1.
Segura Jose Angel MNR SCNDR1: Is Q1 uh sorry.
Ashmore Matt NRT SCNDR1: Yes, young.
Okal Jan GNL SCNDR1: Ohh really mentioned that you put in the vendor batch number.
Okal Jan GNL SCNDR1: Ohh is that mandatory?
Okal Jan GNL SCNDR1: Because that would, I don't think so.
Okal Jan GNL SCNDR1: But I just wanna make sure I understand it correctly.
Okal Jan GNL SCNDR1: The vendor batch number.
Okal Jan GNL SCNDR1: I think it's not mandatory or or is it? Yeah.
Okal Jan GNL SCNDR1: Just want to make sure that that not you know confused.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And you should see that actually the 464 inbound delivery, you should see it there.
Ashmore Matt NRT SCNDR1: I would say.
Segura Jose Angel MNR SCNDR1: Yeah.
Segura Jose Angel MNR SCNDR1: Did one right.
Ashmore Matt NRT SCNDR1: Yes, Sir.
Segura Jose Angel MNR SCNDR1: OK, so here we go again and LED the livery.
Segura Jose Angel MNR SCNDR1: And you process and yes here.
Segura Jose Angel MNR SCNDR1: Umm you can see the the the status in this case the the good recipe is is very complete and also they put away.
Segura Jose Angel MNR SCNDR1: OK.
Segura Jose Angel MNR SCNDR1: Just let's see to create the warehouse task or to see if there is exist an already a warehouse task for for this document you can just uh clicking here and we have two warehouse task.
Segura Jose Angel MNR SCNDR1: There's not completed yet same name.
Segura Jose Angel MNR SCNDR1: Set up just for the status the to do they put away if you can.
Segura Jose Angel MNR SCNDR1: If you want to see or review the the detail for for the warehouse task, you can see here as a list and I think is is is yeah easier to to look into.
Segura Jose Angel MNR SCNDR1: What?
Segura Jose Angel MNR SCNDR1: What is going to do is to go from this storage bin.
Segura Jose Angel MNR SCNDR1: The recent in the year sound because he's already received and put it in this bin.
Segura Jose Angel MNR SCNDR1: The destination bin for this stress type and.
Segura Jose Angel MNR SCNDR1: And.
Segura Jose Angel MNR SCNDR1: They shoe is gonna keep the same.
Segura Jose Angel MNR SCNDR1: Uh, and yes, this uh, some others.
Segura Jose Angel MNR SCNDR1: And specification and characteristic for for this warehouse task.
Segura Jose Angel MNR SCNDR1: So you can click here again.
Segura Jose Angel MNR SCNDR1: And in order to confirm the warehouse task, you only need to put it here and confirm it in in background.
Segura Jose Angel MNR SCNDR1: So there were, Hostess is confirmed.
Segura Jose Angel MNR SCNDR1: You see how how they status change and I want to see this.
Segura Jose Angel MNR SCNDR1: One other was to do a shoes.
Segura Jose Angel MNR SCNDR1: I think that's that's why we have to work off task.
Segura Jose Angel MNR SCNDR1: So I just want to confirm this is the same destination beans and and this other a different issue so.
Segura Jose Angel MNR SCNDR1: I want to confirm this one too.
Segura Jose Angel MNR SCNDR1: Confirming background.
Segura Jose Angel MNR SCNDR1: Guess.
Segura Jose Angel MNR SCNDR1: And now if you want to to review.
Segura Jose Angel MNR SCNDR1: I'm going to take this.
Segura Jose Angel MNR SCNDR1: Number.
Segura Jose Angel MNR SCNDR1: And look in the into the warehouse.
Segura Jose Angel MNR SCNDR1: Uh in the stock overview for for this material.
Segura Jose Angel MNR SCNDR1: Put it here in product the number.
Segura Jose Angel MNR SCNDR1: And this is the material that we moved here.
Segura Jose Angel MNR SCNDR1: I think is this too.
Segura Jose Angel MNR SCNDR1: Ohh yes, we have a uh the batch.
Segura Jose Angel MNR SCNDR1: This this was the same soul.
Segura Jose Angel MNR SCNDR1: Yes, this is confirmed that that the work house that was, uh, do they the correct thing?
Segura Jose Angel MNR SCNDR1: This I don't know you have question from here.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And maybe some information if you explain the stock type status.
Ashmore Matt NRT SCNDR1: If the F1F2 stock type status so everyone is aware.
Segura Jose Angel MNR SCNDR1: Yes, yes.
Segura Jose Angel MNR SCNDR1: Because since we only put this material in in the in the warehouse at this moment in, in this storage type which is of.
Segura Jose Angel MNR SCNDR1: Of 01 and you can review here if you are not sure what it is.
Segura Jose Angel MNR SCNDR1: I miss it.
Segura Jose Angel MNR SCNDR1: Ohh here is the overflow.
Segura Jose Angel MNR SCNDR1: There is one for for the receiving, so it's like a general storage area.
Segura Jose Angel MNR SCNDR1: For for the warehouse type and the the something that is very important here is the stock type.
Segura Jose Angel MNR SCNDR1: Because if your material is how you can know if your material is is has a some kind of of block in it.
Segura Jose Angel MNR SCNDR1: But for example in this one we have F1.
Segura Jose Angel MNR SCNDR1: And this kind of stereotype is unrestrict material use in in put away.
Segura Jose Angel MNR SCNDR1: And all all the unrestricted are are, are, well, are safe to to do some some movement or there's a a different stages for it for because for example here and you see that it's in restrict use for for put away and if I put it in in a different and the warehouse storage type I mean for example inside in in one bin on on on the warehouse area the the stock type will be unrestricted in in warehouse.
Segura Jose Angel MNR SCNDR1: So it changed and for example if if you have like a block in in in that material you're gonna see it by by the stock type.
Segura Jose Angel MNR SCNDR1: For example, if if it's still a missing quality inspection or something else like you did something wrong in when you do the the good receipt or something, they're gonna put the the what do you Aline in?
Segura Jose Angel MNR SCNDR1: One of those statuses.
Ashmore Matt NRT SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: And if there's another way, our if you're looking for available stock for replenishment, you know to production supply or something like that, he chose stock overview, if you choose available stock.
Ashmore Matt NRT SCNDR1: Umm the line right above it.
Ashmore Matt NRT SCNDR1: Jose, if you close that out and go to available stock, you will see what's in stock, stock type status F2, which is available for replenishment.
Segura Jose Angel MNR SCNDR1: No here.
Ashmore Matt NRT SCNDR1: Whose?
Ashmore Matt NRT SCNDR1: Because yours is not showing both as if too, but they indeed are both.
Ashmore Matt NRT SCNDR1: If two, which means they're they're, yeah.
Ashmore Matt NRT SCNDR1: Available for warehouse replenishment. Yeah.
Ashmore Matt NRT SCNDR1: Yes, Sir.
Okal Jan GNL SCNDR1: Ohh there there are both in there.
Okal Jan GNL SCNDR1: But the stock overview doesn't show it at the Hu level, so it just so the available stock will show with two Hu 10 pieces each.
Okal Jan GNL SCNDR1: This one will show you one line with the 20 pieces at yeah.
Segura Jose Angel MNR SCNDR1: This one right?
Ashmore Matt NRT SCNDR1: Yes, you see it there.
Okal Jan GNL SCNDR1: It's 20, even though two Hu, but it's just it's not A to the Hu level to the total level aggregated.
Segura Jose Angel MNR SCNDR1: Maybe if I go to physical stock.
Segura Jose Angel MNR SCNDR1: Yes, you can.
Segura Jose Angel MNR SCNDR1: Do you see there are two of 10?
Ashmore Matt NRT SCNDR1: I think that's simple enough.
Ashmore Matt NRT SCNDR1: That's just.
Ashmore Matt NRT SCNDR1: I mean, that's to put away in the GUI there is also.
Ashmore Matt NRT SCNDR1: Umm you can do the same functionality within the the MOBISYS scanner.
Segura Jose Angel MNR SCNDR1: Mm-hmm. Yeah.
Segura Jose Angel MNR SCNDR1: And also from here if you want to to move it to another location in in the world, how you can do it from here also?
Segura Jose Angel MNR SCNDR1: No, I'm lying.
Segura Jose Angel MNR SCNDR1: It's from here.
Segura Jose Angel MNR SCNDR1: Umm, no, no, I'm totally lie.
Segura Jose Angel MNR SCNDR1: He's he's from physical stock, the one the one?
Segura Jose Angel MNR SCNDR1: Umm, when do you want to move it?
Segura Jose Angel MNR SCNDR1: So maybe just to to do it.
Segura Jose Angel MNR SCNDR1: And.
Segura Jose Angel MNR SCNDR1: So I'm going to put this.
Segura Jose Angel MNR SCNDR1: That just this once come from here.
Segura Jose Angel MNR SCNDR1: If you want to move it, you need to create a work of task.
Segura Jose Angel MNR SCNDR1: So you click on here and show you the the quantity that you have in that.
Segura Jose Angel MNR SCNDR1: So they movement process type is 999 and then.
Segura Jose Angel MNR SCNDR1: Ask you for the destination being.
Segura Jose Angel MNR SCNDR1: If you don't have idea, maybe you can click on the the hobbies and umm.
Segura Jose Angel MNR SCNDR1: It can be with two.
Segura Jose Angel MNR SCNDR1: Uh, for example, on a rack storage.
Segura Jose Angel MNR SCNDR1: Let's see if we have some beans in there created.
Segura Jose Angel MNR SCNDR1: There's many.
Segura Jose Angel MNR SCNDR1: So for example, I gotta put it here.
Segura Jose Angel MNR SCNDR1: I check on the confirmation of the warehouse task.
Segura Jose Angel MNR SCNDR1: So in this way, when you create the warehouse task you you don't need to go and confirm it again.
Segura Jose Angel MNR SCNDR1: So you you only create here and that's it.
Segura Jose Angel MNR SCNDR1: Well, here is his fail there.
Ashmore Matt NRT SCNDR1: And that's yeah, but that the reason it failed is because that at that stores location already has an Hu within it.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: So within the right storage are not allowed are in this current configuration, you're not allowed to store multiple Hu.
Ashmore Matt NRT SCNDR1: So if you go back Jose and choose one that has the empty flag selected for the yeah, no.
Segura Jose Angel MNR SCNDR1: Uh, sorry, sorry.
Segura Jose Angel MNR SCNDR1: Yes, I do the same here.
Segura Jose Angel MNR SCNDR1: Yes.
Segura Jose Angel MNR SCNDR1: So I got gonna go here and create the warehouse task again and then and then.
Segura Jose Angel MNR SCNDR1: Brooke Taurus.
Segura Jose Angel MNR SCNDR1: And yes, I sent might mention I will select one the for example this one right Matt?
Segura Jose Angel MNR SCNDR1: They'll have to check here.
Ashmore Matt NRT SCNDR1: Yeah, you can choose.
Ashmore Matt NRT SCNDR1: Wait, you need one that says AHL, so choose the it's close to the top and that one is OK, yeah.
Segura Jose Angel MNR SCNDR1: Yeah.
Segura Jose Angel MNR SCNDR1: Sure, and create the work of task.
Segura Jose Angel MNR SCNDR1: And it's also OK, so let's try 11 again, there's two.
Segura Jose Angel MNR SCNDR1: I think one is for the section now.
Segura Jose Angel MNR SCNDR1: Maybe if I choose like this one?
Segura Jose Angel MNR SCNDR1: The team.
Segura Jose Angel MNR SCNDR1: Oh.
Segura Jose Angel MNR SCNDR1: Look for putaway so well.
Ashmore Matt NRT SCNDR1: And let me check into why that's happening, but typically you can move it.
Ashmore Matt NRT SCNDR1: And this was actually, I assume, set up to to go to the rack storage, but overflow is is designed so once the racks are full to to go to the to the overflow area, but to put away strategy should have been to Mrs Ohh 01 but I'm.
Ashmore Matt NRT SCNDR1: Actually, Jose looks like a bad example because it has no.
Ashmore Matt NRT SCNDR1: Working so actually didn't have a warehouse product.
Ashmore Matt NRT SCNDR1: If if you show them the.
Ashmore Matt NRT SCNDR1: Warehouse product master, you know.
Segura Jose Angel MNR SCNDR1: Mm-hmm.
Ashmore Matt NRT SCNDR1: And then go to the warehouse.
Segura Jose Angel MNR SCNDR1: It's not working here.
Segura Jose Angel MNR SCNDR1: Yeah, exactly so.
Segura Jose Angel MNR SCNDR1: So yes, this is like a the master data for for the warehouse like we had a master data for each material, we should have a master data for this warehouse product for EWM and in this case the there were hostap is not extended right or created.
Segura Jose Angel MNR SCNDR1: Yeah.
Segura Jose Angel MNR SCNDR1: So maybe if I pick the.
Segura Jose Angel MNR SCNDR1: The new delivery that Renee help us, maybe in that one could work.
Segura Jose Angel MNR SCNDR1: Ohh OK.
Ashmore Matt NRT SCNDR1: But I think.
Ashmore Matt NRT SCNDR1: For today you seen how for today you've seen how to confirm the warehouse task.
Segura Jose Angel MNR SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: Jose for maybe the next session or or one of the following sessions.
Ashmore Matt NRT SCNDR1: I would like for you to please go over the master data with them.
Ashmore Matt NRT SCNDR1: The warehouse product master and how I shall be set up and in order to drive the put away strategy and the and the the what is it called?
Ashmore Matt NRT SCNDR1: Put away control and and uh stock removal control keys so.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And and Jan if you would like to then move into the production logistics side, maybe create a A.
Ashmore Matt NRT SCNDR1: Production order.
Ashmore Matt NRT SCNDR1: Then we can do a a simple replenishment.
Ashmore Matt NRT SCNDR1: Hopefully there's some stock.
Ashmore Matt NRT SCNDR1: No.
Ashmore Matt NRT SCNDR1: You can use any product because I don't know what we will have inventory for in in ZQ one.
Okal Jan GNL SCNDR1: I let's check if we're this one is.
Okal Jan GNL SCNDR1: Being used, if we if we could use this material and ask for it.
Okal Jan GNL SCNDR1: Ohh, even though without warehouse perk it will not work.
Okal Jan GNL SCNDR1: Oh crap.
Okal Jan GNL SCNDR1: Ohh second.
Okal Jan GNL SCNDR1: So.
Ashmore Matt NRT SCNDR1: And at anytime anyone can jump in and make comments.
Ashmore Matt NRT SCNDR1: Suggestions.
Ashmore Matt NRT SCNDR1: Don't.
Ashmore Matt NRT SCNDR1: Don't just leave it to the ones who are presenting, yeah.
Okal Jan GNL SCNDR1: Yeah, I and I am in Z Q1 and as everybody is familiar with ZQ one, it's not really clear what the setups are at any particular moment for any particular part, but.
Okal Jan GNL SCNDR1: This is the part we usually used for.
Okal Jan GNL SCNDR1: The so semi finished parts production.
Okal Jan GNL SCNDR1: So we let me just check few settings just so we know BNOM1.
Okal Jan GNL SCNDR1: Ohh two friend, but OK we we can try to open production order.
Okal Jan GNL SCNDR1: Let's see if if it works.
Okal Jan GNL SCNDR1: So we're opening production order is for the inner Co 01 transaction for the discrete manufacturing plug in the part number.
Okal Jan GNL SCNDR1: Ohh it premature master selects specific order type.
Okal Jan GNL SCNDR1: There are these order types.
Okal Jan GNL SCNDR1: Ohh similar ones.
Okal Jan GNL SCNDR1: We have also for the other plans, I think with the exception of Kanban orders for ECHO, but the PA&P are are P00 for production or assembly for semi finished and finished goods parts.
Okal Jan GNL SCNDR1: The PKPR&PD and P00.
Okal Jan GNL SCNDR1: They are more for rework, dismantling prototypes and stuff like that, and that is mostly happening outside of the EWM upper template.
Okal Jan GNL SCNDR1: So automatically spools the PA00 with 400 pieces or current day.
Okal Jan GNL SCNDR1: Ohh and.
Okal Jan GNL SCNDR1: It will try to.
Okal Jan GNL SCNDR1: Ohh, schedule the order.
Okal Jan GNL SCNDR1: If all the setups are correct, there's not much need to change anything in the in the setups.
Okal Jan GNL SCNDR1: Ohh, one thing from the testing.
Okal Jan GNL SCNDR1: Uh, sometimes the goods receipt.
Okal Jan GNL SCNDR1: Uh was trying to put it.
Okal Jan GNL SCNDR1: Ohh not where we wanted to for the finished goods, you may try to either go to the production supply area and then move it later or put it directly to the finished goods area depending depending on what plan wants.
Okal Jan GNL SCNDR1: And also you will see if it will likely to trigger any quality inspection records or not from the goods receipt confirmation.
Okal Jan GNL SCNDR1: Sure.
Okal Jan GNL SCNDR1: So I'm not changing anything in this moment.
Okal Jan GNL SCNDR1: I would just release the order, which is this button here.
Okal Jan GNL SCNDR1: Uh usually tries to do the material availability check and would start telling you if you don't have any materials needed or in the respective areas to run these order.
Okal Jan GNL SCNDR1: Usually the users will either go with it or ignore that you can check the missing list parts.
Okal Jan GNL SCNDR1: So here this is the part we are missing in the production area.
Okal Jan GNL SCNDR1: So we'll need to ask for it.
Okal Jan GNL SCNDR1: You can or we can release the order on.
Okal Jan GNL SCNDR1: Nevertheless, even with the.
Okal Jan GNL SCNDR1: Missing parts and just supply the parts later so I will release the order and this case and save.
Okal Jan GNL SCNDR1: Ohh based on the settings, the saving of the order will print the order and the release of the order will based on the setup create possibly create warehouse tasks to bring them material into the PSA's.
Okal Jan GNL SCNDR1: It depends on the settings for echo.
Okal Jan GNL SCNDR1: We turned it off for surely I think they have it turned on.
Okal Jan GNL SCNDR1: So wish I can check the spool if this part as even set up to print something.
Okal Jan GNL SCNDR1: OK, so this part number is the production order printout.
Okal Jan GNL SCNDR1: Turn off.
Okal Jan GNL SCNDR1: I think this might be something which is trying to trick you.
Okal Jan GNL SCNDR1: Not printing it all or possibly communicating with MES system through MI.
Okal Jan GNL SCNDR1: I don't know what they are testing now.
Okal Jan GNL SCNDR1: Ohh, after the production is open we can check it in a sealed 03.
Okal Jan GNL SCNDR1: This showed me the order I opened.
Okal Jan GNL SCNDR1: Check the operations all have two operations.
Okal Jan GNL SCNDR1: One milestone that's ZP 6.
Okal Jan GNL SCNDR1: And As for components.
Okal Jan GNL SCNDR1: We have 3 components.
Okal Jan GNL SCNDR1: Will be consuming one of them.
Okal Jan GNL SCNDR1: We didn't have ohh in the production supply area.
Okal Jan GNL SCNDR1: The other ones are are I guess available.
Okal Jan GNL SCNDR1: Ohh this I guess it's exception.
Okal Jan GNL SCNDR1: This is a bellman component testing I.
Okal Jan GNL SCNDR1: To be honest, I don't know how the bellman components are working at this moment in ZQ one.
Okal Jan GNL SCNDR1: What is it doing?
Okal Jan GNL SCNDR1: But it looks like we didn't commit anything and it didn't ask ohh as a missing material during availability check.
Okal Jan GNL SCNDR1: To confirm production or.
Okal Jan GNL SCNDR1: Shall we confirm or ask for material now? Ohh.
Ashmore Matt NRT SCNDR1: Yeah, we can't.
Ashmore Matt NRT SCNDR1: We can ask for material there.
Ashmore Matt NRT SCNDR1: There's two.
Ashmore Matt NRT SCNDR1: There's multiple options with Kanban or our production supply.
Ashmore Matt NRT SCNDR1: Maybe Jan, you can if you would like you can explain or.
Okal Jan GNL SCNDR1: They yeah.
Okal Jan GNL SCNDR1: Ohh again we we had the option.
Okal Jan GNL SCNDR1: We can check if something was generated in a or in a monitor, but I don't think I've seen it.
Okal Jan GNL SCNDR1: May not be set up in Asik, you or I don't know what is set up here, but we have either manual replacement or warehouse task created from the production order release or some Kanban setups in this case.
Okal Jan GNL SCNDR1: I'm not sure what he said up here.
Okal Jan GNL SCNDR1: Let me just check if we have any warehouse tasks.
Okal Jan GNL SCNDR1: Ohh come on.
Okal Jan GNL SCNDR1: OK, Anna.
Okal Jan GNL SCNDR1: And the monitor, I think we would see the warehouse task and inbound OK documents, inbound delivery warehouse order.
Okal Jan GNL SCNDR1: Is it a warehouse holder?
Okal Jan GNL SCNDR1: Or warehoused.
Segura Jose Angel MNR SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: Yeah, you, you you want to see the for that particular production order, correct?
Ashmore Matt NRT SCNDR1: Yeah, you can put a.
Okal Jan GNL SCNDR1: Ohh was it?
Okal Jan GNL SCNDR1: It was not.
Segura Jose Angel MNR SCNDR1: But, but I think it's it's in the folder the documents below because in that one going to send you the email yes then that one.
Okal Jan GNL SCNDR1: No, it's we had one where you I could have plugged in a production order.
Okal Jan GNL SCNDR1: I don't see it here.
Okal Jan GNL SCNDR1: It was under a.
Okal Jan GNL SCNDR1: Transfers or press for orders or something like that, not transporters.
Okal Jan GNL SCNDR1: Stock transfers possibly here.
Okal Jan GNL SCNDR1: No.
Okal Jan GNL SCNDR1: I didn't know word.
Okal Jan GNL SCNDR1: For householders.
Okal Jan GNL SCNDR1: Oh, here is on the posting changes.
Okal Jan GNL SCNDR1: On the documents.
Segura Jose Angel MNR SCNDR1: Alright, OK.
Okal Jan GNL SCNDR1: Ohh here when unplugged in Ohh can plug in the manufacturing order.
Okal Jan GNL SCNDR1: Ohh it would show the warehouse.
Okal Jan GNL SCNDR1: Uh transfers for material created.
Okal Jan GNL SCNDR1: This one doesn't have that set up, but we had it in Echo and think Ohh Christina did send see it also in Fenton Paulo.
Okal Jan GNL SCNDR1: Well, if I'm not mistaken.
Okal Jan GNL SCNDR1: So at this moment I don't see any or householders created for this.
Okal Jan GNL SCNDR1: Specific order.
Okal Jan GNL SCNDR1: One to one.
Okal Jan GNL SCNDR1: 2nd.
Okal Jan GNL SCNDR1: We can try to pull in components for sure this particular one.
Okal Jan GNL SCNDR1: Was missing.
Okal Jan GNL SCNDR1: We can check the availability available stock.
Okal Jan GNL SCNDR1: OK, so we have stock.
Okal Jan GNL SCNDR1: Everywhere.
Okal Jan GNL SCNDR1: Or simply we could create a uh with this request.
Okal Jan GNL SCNDR1: You could eat them, push something from here to the production supply area.
Okal Jan GNL SCNDR1: Or we could ask for the material with the scanner to be delivered.
Ashmore Matt NRT SCNDR1: Yeah, we we can do either one.
Ashmore Matt NRT SCNDR1: I right now need to create a production control cycle though and there isn't one.
Ashmore Matt NRT SCNDR1: Ark control cycle, yeah.
Okal Jan GNL SCNDR1: We don't have any.
Ashmore Matt NRT SCNDR1: No.
Okal Jan GNL SCNDR1: So that would be in the became C.
Ashmore Matt NRT SCNDR1: Mm-hmm.
Ashmore Matt NRT SCNDR1: You can search for the part if you would like to, yeah.
Ashmore Matt NRT SCNDR1: This material here.
Okal Jan GNL SCNDR1: Ohh here yeah.
Okal Jan GNL SCNDR1: Uh, sorry, not that one, the.
Okal Jan GNL SCNDR1: We have nothing.
Segura Jose Angel MNR SCNDR1: No.
Okal Jan GNL SCNDR1: OK, so we need to create 1 correct?
Ashmore Matt NRT SCNDR1: Yeah, you would need to create one. Yeah.
Ashmore Matt NRT SCNDR1: Click there.
Ashmore Matt NRT SCNDR1: Yeah, the pencil.
Okal Jan GNL SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And then the little paper is the second one from the end.
Okal Jan GNL SCNDR1: OK.
Okal Jan GNL SCNDR1: So the tierro.
Okal Jan GNL SCNDR1: Ohh, do you really want us to create this in A and ZSQL1?
Okal Jan GNL SCNDR1: Cause I have no idea.
Okal Jan GNL SCNDR1: I have no idea who is this thing.
Okal Jan GNL SCNDR1: What I'm really cautious about creating things like here when they end up possibly in the middle of testing other stuff.
Ashmore Matt NRT SCNDR1: So let's don't for this.
Ashmore Matt NRT SCNDR1: For this case, we will look for a command control cycle and do the replenishment in the next call.
Ashmore Matt NRT SCNDR1: Let's don't create one.
Ashmore Matt NRT SCNDR1: I'll find one that's already created and into a replenishment and that way just with the standard classical combine, so you can just finish out the confirmation, then we'll have a COGI error, yeah.
Okal Jan GNL SCNDR1: So it yeah, in this case I would try to just do the confirmation for the order.
Okal Jan GNL SCNDR1: So here we have two options, 22 basic options.
Okal Jan GNL SCNDR1: We could create a the plant handling units and do the confirmation of operations in the CO11 and and then goods receipt in Cowboy transaction.
Okal Jan GNL SCNDR1: Or we could use the Mobisys transaction and do this all in one step if everything is set up correctly.
Okal Jan GNL SCNDR1: So let's do the maybe the first one.
Okal Jan GNL SCNDR1: Ohh partially with this order 400 so we can try to do half half so in COOP 1 sorry.
Okal Jan GNL SCNDR1: This is the order I can create a plan handling unit for.
Okal Jan GNL SCNDR1: It looks like we have five pieces, so let's open for.
Okal Jan GNL SCNDR1: Just one Hu for this one, four to five pieces. Ohh.
Okal Jan GNL SCNDR1: I will save it, I can create all of them, or just one or less, or save it.
Okal Jan GNL SCNDR1: It creates the.
Okal Jan GNL SCNDR1: Hu This this is the Hu.
Okal Jan GNL SCNDR1: Will compare this wall and.
Okal Jan GNL SCNDR1: Are you can go to the confirmation first.
Okal Jan GNL SCNDR1: That's the CONF11 N.
Okal Jan GNL SCNDR1: Transaction.
Okal Jan GNL SCNDR1: I will select.
Okal Jan GNL SCNDR1: You can either suck operation Tenant operation 20 or just the milestone as this is the milestone here, I will select it.
Okal Jan GNL SCNDR1: I will confirm the five pieces.
Okal Jan GNL SCNDR1: Uh, we can check.
Okal Jan GNL SCNDR1: Uh, the goods movement, which would be happening if you just push the button.
Okal Jan GNL SCNDR1: So here these are the two components which would be.
Okal Jan GNL SCNDR1: Consumed during this operation during this confirmation.
Okal Jan GNL SCNDR1: So I will save.
Okal Jan GNL SCNDR1: I will get the.
Okal Jan GNL SCNDR1: Message that basically both of the.
Okal Jan GNL SCNDR1: Materials were not available on the consumption failed and will be on a COGI and I will now do the second portion as the copper transaction.
Okal Jan GNL SCNDR1: This is the order we can propose.
Okal Jan GNL SCNDR1: The Hu this is the one which is available which I created and I can just ohh.
Okal Jan GNL SCNDR1: So I didn't save it.
Okal Jan GNL SCNDR1: We shall get.
Okal Jan GNL SCNDR1: OK, error message OK. Message.
Okal Jan GNL SCNDR1: So now we can check in the order and see if 03.
Okal Jan GNL SCNDR1: The status of the order and we'll see.
Okal Jan GNL SCNDR1: We delivered five pieces.
Okal Jan GNL SCNDR1: If you look at the operations, we confirmed five pieces on both operations.
Okal Jan GNL SCNDR1: And we can check also the Kogi for this order.
Okal Jan GNL SCNDR1: We shall see the.
Okal Jan GNL SCNDR1: Components which are waiting for consumption.
Okal Jan GNL SCNDR1: So these two components are waiting.
Okal Jan GNL SCNDR1: You could possibly try to process this one.
Okal Jan GNL SCNDR1: Ohh.
Okal Jan GNL SCNDR1: It's trying to consume from here.
Okal Jan GNL SCNDR1: From the PSA 01 and the PSA bin.
Okal Jan GNL SCNDR1: Or just mark it for me.
Okal Jan GNL SCNDR1: So I don't forget.
Okal Jan GNL SCNDR1: Yeah.
Okal Jan GNL SCNDR1: So we could in the.
Okal Jan GNL SCNDR1: On a tour.
Okal Jan GNL SCNDR1: That is my monitor so available stock.
Okal Jan GNL SCNDR1: I could move some of these pieces with the warehouse task to that area.
Okal Jan GNL SCNDR1: I could create the warehouse task.
Okal Jan GNL SCNDR1: And move it to the.
Okal Jan GNL SCNDR1: I think it's a Beast 001.
Okal Jan GNL SCNDR1: And.
Okal Jan GNL SCNDR1: It was the PSA bin.
Okal Jan GNL SCNDR1: Or is it the on 2nd my check?
Okal Jan GNL SCNDR1: I think it's this one here.
Okal Jan GNL SCNDR1: OK so.
Okal Jan GNL SCNDR1: It we moved it by the way, we could also do a partial move if we need to consume 5 pieces, we could move out of the warehouse during then we only five pieces.
Okal Jan GNL SCNDR1: That's also option.
Okal Jan GNL SCNDR1: Let's go back to the COGI.
Okal Jan GNL SCNDR1: I could try to reprocess it should find itself now.
Okal Jan GNL SCNDR1: And didn't it?
Okal Jan GNL SCNDR1: Ask me why now.
Ashmore Matt NRT SCNDR1: And actually this is a question that I have for.
Okal Jan GNL SCNDR1: I wanna check.
Okal Jan GNL SCNDR1: Yeah.
Okal Jan GNL SCNDR1: One thing I know they're working on or it's in the process of doing something is the output determination based on the valuation class.
Okal Jan GNL SCNDR1: Because currently if you have parts which are set up with a split valuation for in, how for purchase parts and in-house produce and in-house produce would not it has a hard time selecting it automatically.
Okal Jan GNL SCNDR1: So it may be something which is just not working at this moment.
Okal Jan GNL SCNDR1: I would I think this is the one I moved there.
Okal Jan GNL SCNDR1: Or is the badge here?
Okal Jan GNL SCNDR1: I can try if it will process.
Okal Jan GNL SCNDR1: Uh, it didn't process.
Okal Jan GNL SCNDR1: But we shouldn't.
Okal Jan GNL SCNDR1: External I external bike trip third party.
Okal Jan GNL SCNDR1: Pay.
Okal Jan GNL SCNDR1: I don't know why it didn't consume normally Edward.
Okal Jan GNL SCNDR1: But there are many things in the Q1 which we don't know.
Okal Jan GNL SCNDR1: I'm not sure why didn't consume met.
Ashmore Matt NRT SCNDR1: Yeah, I'm not either when anyone, I mean should.
Ashmore Matt NRT SCNDR1: Should we use the M1 for testing in the future or ZM one is is terrible in my opinion.
Okal Jan GNL SCNDR1: I I I think I think I think very soon, next week we shall have the ZSSE1 available with the copy of the data from ZT1 and Ohh that works more or less much better.
Okal Jan GNL SCNDR1: Yeah, there was some issue with the missing data or MasterData originally.
Okal Jan GNL SCNDR1: But whatever we had as the data for testing that was all corrected and it's it's basically working.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: From Jose that that should work, we can use ECHO if you would like young, but we can still replenish there and actually do another production ordering confirmation so you can see how it it it shall work.
Okal Jan GNL SCNDR1: Ohh yeah, how it works without glitches.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: That shouldn't shouldn't take so long?
Ashmore Matt NRT SCNDR1: Umm.
Ashmore Matt NRT SCNDR1: On Friday and then maybe we can start looking into either going into outbound George or or Paula, whatever you guys think are we can take a more in depth look into some MasterData settings and what is prelevant for.
Ashmore Matt NRT SCNDR1: Like the warehouse?
Ashmore Matt NRT SCNDR1: Uh production.
Ashmore Matt NRT SCNDR1: Monitor whatever it's called the warehouse product master.
Ashmore Matt NRT SCNDR1: Maybe look into some more.
Ashmore Matt NRT SCNDR1: Relevant things there.
Ashmore Matt NRT SCNDR1: What do you guys suggest?
Ashmore Matt NRT SCNDR1: This is an open forum, so there's no real set agenda.
Ashmore Matt NRT SCNDR1: We just need to to learn from each other.
Ashmore Matt NRT SCNDR1: Someone put out some some training material for others to learn.
Ashmore Matt NRT SCNDR1: Each week, that's.
Ashmore Matt NRT SCNDR1: That's the whole point of this meeting.
Ashmore Matt NRT SCNDR1: Any proposals from anyone?
Okal Jan GNL SCNDR1: I would suggest that we also do the testing using the Mobisys transaction, but I don't wanna do it in the ZQ one because that's always problem.
Ashmore Matt NRT SCNDR1: Sure, we can do that.
Kota Christina AHL SCNDR1: You know, it's not our area, but I'd like to see the different ways for scrap.
Kota Christina AHL SCNDR1: When we scrap materials.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And is that something that you can share with us?
Okal Jan GNL SCNDR1: Ohh we can try.
Okal Jan GNL SCNDR1: Yeah.
Okal Jan GNL SCNDR1: When the in the RG.
Ashmore Matt NRT SCNDR1: OK.
Okal Jan GNL SCNDR1: Yeah.
Okal Jan GNL SCNDR1: And by the way, ohh I'd know one second.
Okal Jan GNL SCNDR1: Maybe this is ohh something I may wanna share with us with you ask.
Okal Jan GNL SCNDR1: Maybe with Christina I got the reply from our friend and Andrea Kiefer regarding the.
Okal Jan GNL SCNDR1: OK, from controlling to use the uh scribe reporting in Co 11 N.
Kota Christina AHL SCNDR1: OK.
Kota Christina AHL SCNDR1: Well, they're.
Okal Jan GNL SCNDR1: So we had still questions about the repetitive manufacturing, which I'm clarifying whether.
Okal Jan GNL SCNDR1: So I didn't release it yet officially, but the she agreed with this two things, which is are in SAP when we do the confirmation.
Okal Jan GNL SCNDR1: And enter the order.
Okal Jan GNL SCNDR1: Ohh it would be trying to report scrap.
Okal Jan GNL SCNDR1: I don't know.
Okal Jan GNL SCNDR1: I produced five pieces and four pieces are OK and one piece is bad and it basically would allow something to be confirmed and consumes labor and consumes components.
Okal Jan GNL SCNDR1: But with the 261 against the order.
Kota Christina AHL SCNDR1: Yeah, I think they're leaning more towards using ego and they're trying to figure out right now how to, uh, create an Excel macro and a VB.
Okal Jan GNL SCNDR1: I would discourage.
Kota Christina AHL SCNDR1: VBS script and SAP.
Okal Jan GNL SCNDR1: I would discourage them because in amigo it will create the deliveries which somebody will need to process in EWM because this might be a better transaction to use this for the scrap.
Kota Christina AHL SCNDR1: Yeah, I I know it.
Kota Christina AHL SCNDR1: I know it's not.
Kota Christina AHL SCNDR1: It's not our, I guess it's not our decision and we gotta where waiting for.
Kota Christina AHL SCNDR1: We're waiting for them.
Okal Jan GNL SCNDR1: But they if they decide to do something, we should takes more time.
Okal Jan GNL SCNDR1: They shall stop telling us they didn't extra 40 people to do it.
Kota Christina AHL SCNDR1: Yeah, I know, I know.
Ashmore Matt NRT SCNDR1: Yeah.
Okal Jan GNL SCNDR1: But anyway, yeah.
Ashmore Matt NRT SCNDR1: So yeah, so so for next, so for Friday, let's let's shoot for using ZSSE1, we will do a replenishment from, in, within ZE one and then also do the production order.
Ashmore Matt NRT SCNDR1: Once again, the following meeting George or Paula, would you guys have any objections of going over, let's say, a basic shipment?
Stevaux Jorge SRC SCNDR1: I won't be here on Friday night because of that the department meeting.
Ashmore Matt NRT SCNDR1: Yeah, but on Monday, Monday, the next meeting, not Friday.
Ashmore Matt NRT SCNDR1: Friday we will do the production.
Stevaux Jorge SRC SCNDR1: No, no problem.
Ashmore Matt NRT SCNDR1: If it's possible, I don't know where we're at today with with outbound.
Stevaux Jorge SRC SCNDR1: Yeah, you did. Follow me.
Stevaux Jorge SRC SCNDR1: Can we can do some one scenario one in 20 scenario there create a sales order or create a delivery.
Ashmore Matt NRT SCNDR1: Yeah. OK.
Ashmore Matt NRT SCNDR1: Yeah, that that would be great.
Ashmore Matt NRT SCNDR1: Jorge, you have a question?
Giron Jorge MNR SCNDR1: Now it's more a proposal, but I think we have a an opportunity area regarding the master data.
Giron Jorge MNR SCNDR1: I don't know if it is work to bring somebody from MDG expert that can give us also like uh.
Giron Jorge MNR SCNDR1: Like Jose said that for example, if we can get some templates that we can reach out just to know to check if all the masters setup of the materials is good to go.
Giron Jorge MNR SCNDR1: You know, I don't know, just thinking out loud.
Ashmore Matt NRT SCNDR1: Yes, so.
Ashmore Matt NRT SCNDR1: Separately, we are working on something our our in discussions for something that will be an automated tool that will check the data consistency according to the what what.
Ashmore Matt NRT SCNDR1: The global DTA, say is the.
Ashmore Matt NRT SCNDR1: This call it standard parameters within the MasterData.
Ashmore Matt NRT SCNDR1: Once that tool is ready, then for sure it will be will be shown to everyone.
Ashmore Matt NRT SCNDR1: I think that will make things a little bit easier for us in regards to data consistency or inconsistency, yeah.
Ashmore Matt NRT SCNDR1: I don't know that we can get someone from the MDG.
Ashmore Matt NRT SCNDR1: I struggle with MDG anyway in general, so I'm not sure that that I could get someone to participate.
Ashmore Matt NRT SCNDR1: I can.
Ashmore Matt NRT SCNDR1: I can try but yeah I think if.
Stevaux Jorge SRC SCNDR1: Yeah, or even maybe between us, we can share our knowledge between the different men fields.
Stevaux Jorge SRC SCNDR1: And I don't know if that's what Jorge was also talking like.
Stevaux Jorge SRC SCNDR1: I don't know.
Stevaux Jorge SRC SCNDR1: They are basically data 11, some I don't know if that's the direction we were going or planning to go, but OK, what we know that it's important in this tab or I we know.
Stevaux Jorge SRC SCNDR1: Ohh, I'd learned that and during the integration test we had an issue here because sales and data one for example we must have this field whatever I don't.
Stevaux Jorge SRC SCNDR1: And then we can share our knowledge.
Stevaux Jorge SRC SCNDR1: I think everyone knows a little bit of the different fields.
Stevaux Jorge SRC SCNDR1: I don't know everything really about the different fields of the master data.
Stevaux Jorge SRC SCNDR1: What I know is that the impact a lot every process.
Ashmore Matt NRT SCNDR1: Yeah, you're right.
Stevaux Jorge SRC SCNDR1: We learned from each other the different everyone.
Stevaux Jorge SRC SCNDR1: Everybody probably had an issue there and we can what everybody knows here from basically data.
Stevaux Jorge SRC SCNDR1: Ohh and then we share.
Stevaux Jorge SRC SCNDR1: I don't know the goal.
Stevaux Jorge SRC SCNDR1: Basically data one.
Stevaux Jorge SRC SCNDR1: Basically, data 2 whatever sales 1/2 and 312 and general data whatever.
Ashmore Matt NRT SCNDR1: Yeah, again, there's no set agenda, any any proposal or any ideas, anything that we would like to see, that's what we should utilize this for to learn and build our knowledge together.
Okal Jan GNL SCNDR1: I I would agree with George that would make sense to exchange these, OK, this is what was missing and it we couldn't do something or it crashed something or failed.
Stevaux Jorge SRC SCNDR1: This would even help us with the DMD or whatever.
Stevaux Jorge SRC SCNDR1: Yeah, demo file for the integration test.
Ashmore Matt NRT SCNDR1: So do we like to do that to Friday or proposed to wait till Monday when when George and Humata will be available?
Stevaux Jorge SRC SCNDR1: Yeah, Monday would be fine for me.
Stevaux Jorge SRC SCNDR1: I don't know about, you know.
Okal Jan GNL SCNDR1: Ohh what would be would be good for them to benefit from it as well.
Giron Jorge MNR SCNDR1: Yeah.
Giron Jorge MNR SCNDR1: And I think this is a set #1 right to to have the correct master data set up for to, you know, to work for all the processes.
Giron Jorge MNR SCNDR1: So yeah, we'll agree also.
Ashmore Matt NRT SCNDR1: In the other feedback, yeah.
Ashmore Matt NRT SCNDR1: If not then then then for sure on Monday we start, we start doing this and and working together to identify the fields and what is the the correct.
Ashmore Matt NRT SCNDR1: Information or to input for that field, yeah.
Segura Jose Angel MNR SCNDR1: Yeah, just one.
Segura Jose Angel MNR SCNDR1: One doubt, Matt, regarding the system where we're going to do the the test just to to review the the data, there is CL1.
Segura Jose Angel MNR SCNDR1: OK, understood.
Okal Jan GNL SCNDR1: I I I just hope that we will get with the copy.
Ashmore Matt NRT SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: Is should be.
Ashmore Matt NRT SCNDR1: It should be available Monday morning.
Ashmore Matt NRT SCNDR1: Yes, European time and that that is the according to all the information I've received, they they will umm move this access and the the information data as well so.
Ashmore Matt NRT SCNDR1: Be a copy, but let's see if it's not available.
Ashmore Matt NRT SCNDR1: Then again on on Friday, we may start looking into MasterData topics and start reviewing them as well.
Ashmore Matt NRT SCNDR1: Alright guys.
Ashmore Matt NRT SCNDR1: Thank you.
Ashmore Matt NRT SCNDR1: Thank you for your time.
Ashmore Matt NRT SCNDR1: Again, it's completely flexible meeting anytime you wanna discuss something or bring a point up, just let us know and then hopefully one of us can prepare something and and present it to help clarify questions or concerns that that you may have.
Cabrera Bobadilla Abraham (BGSW/EBS1-SDS) (Unverified): Thank you.
Westrup Renee MNR SCNDR1: Bye. Thanks.
Ashmore Matt NRT SCNDR1: Yes, Sir.
Okal Jan GNL SCNDR1: Matt, I have made I have one question, could I call you or can we can you stay here?
Okal Jan GNL SCNDR1: They then stay here.
