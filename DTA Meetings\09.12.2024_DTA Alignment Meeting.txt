<PERSON><PERSON> Matt NRT SCNDR1: We will do something very similar to this for for ECHO, but <PERSON>, I have a question.
<PERSON> AHL SCNDR1: Yeah. Well, first, a statement.
<PERSON> AHL SCNDR1: I I escalated this topic multiple times during the Fenton Fowlerville project.
<PERSON> AHL SCNDR1: I did everything possible.
<PERSON> AHL SCNDR1: I sent this training out over a year ago.
<PERSON> AHL SCNDR1: I've sent this training out again.
<PERSON> AHL SCNDR1: Fell every couple months after and and I asked <PERSON><PERSON> to get with <PERSON> and all I I did as much as I possibly could to get these trainings completed and it was not completed.
<PERSON> AHL SCNDR1: <PERSON> is going much in the same way they I provided the trainings to the site lead.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: Yeah. So I would.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: I would propose here.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: Not to talk so much about the history.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: So <PERSON>, your initiative is or your engagement is highly appreciated.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: We know that many escalations.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: Were not followed up in the past.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: We are where we are and we need to work with what we have.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: Yeah. At the moment that is the reality.
<PERSON><PERSON><PERSON> Jens FRD SCNDR: So, but talking about the future. Yeah, so for <PERSON><PERSON> and Fowlerville, we need to cover that.
<PERSON>eler Jens FRD SCNDR: That is initiated that is on the way.
Hadeler Jens FRD SCNDR: We will definitively not reach 100%.
Hadeler Jens FRD SCNDR: Yeah, but we should do what is possible to do.
Ashmore Matt NRT SCNDR1: Mm hmm.
Hadeler Jens FRD SCNDR: And for the other projects, we need to learn from that.
Hadeler Jens FRD SCNDR: Yeah. So this is a simple overview and to be honest, if I think about then I ask myself the question why didn't we create such a simple overview half a year ago?
Hadeler Jens FRD SCNDR: Yeah, because that is something.
Hadeler Jens FRD SCNDR: What what makes visible about the situation?
Ashmore Matt NRT SCNDR1: Yeah. So moving forward, we're gonna. We're gonna start this activity a little bit very early.
Ashmore Matt NRT SCNDR1: So we gonna do this in the next weeks for ECHO and then we'll do it for Marshall and Lamera. Even though Lamera has a different timeline. It's good that we go ahead and do something very similar to this.
Ashmore Matt NRT SCNDR1: Sorry not to focus so much on the the.
Ashmore Matt NRT SCNDR1: My HR suite trainings.
Ashmore Matt NRT SCNDR1: But there was some onsite training and communications done during the completeness check.
Ashmore Matt NRT SCNDR1: We all know that the functional test.
Ashmore Matt NRT SCNDR1: To not all the activities were available in in the timing due to various issues, but the integration test we were able to complete some areas outbound wasn't yet at a maturity level to do so, so.
Ashmore Matt NRT SCNDR1: Paul and I aligned.
Ashmore Matt NRT SCNDR1: It was around 30% there and then again for the UAT, there's still some open topics we weren't able to complete, but.
Ashmore Matt NRT SCNDR1: In general, the the picture you can see here and also the.
Ashmore Matt NRT SCNDR1: Key user end user training hasn't been conducted and we are three weeks before go live in Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: So it's a tall hill to climb.
Ashmore Matt NRT SCNDR1: I second what Ian said. We should have done this much earlier.
Ashmore Matt NRT SCNDR1: Nevertheless, we have it now and it's a way forward. I know infinite Fallowfield.
Ashmore Matt NRT SCNDR1: They're working to, to.
Ashmore Matt NRT SCNDR1: Establish these trainings. These end user trainings. Karen Muller is really taking a a lead on that and supporting Alling and getting the trainings.
Ashmore Matt NRT SCNDR1: Out to the end users as well.
Ashmore Matt NRT SCNDR1: With the support, with the support we're not conducting, let's make it clear we're not conducting those trainings. We have a.
Ashmore Matt NRT SCNDR1: We have a project scope or a directed from the project management.
Ashmore Matt NRT SCNDR1: And the accusers are responsible for conducting that training. But we do need to support I.
Ashmore Matt NRT SCNDR1: 'M fully alignment with Alan that the accusers don't have a even a 50% clear picture of the S4 HANA.
Ashmore Matt NRT SCNDR1: But they should have enough by now to to be able to conduct the basic level training for the for the key users, and we can support them. Yes, Paula.
Caballero Aldana Paula AHL SCNDR1: So Mike told me yesterday that that's not the case.
Caballero Aldana Paula AHL SCNDR1: He doesn't even understand 50% of it, so he doesn't feel ready.
Caballero Aldana Paula AHL SCNDR1: To even do it himself.
Caballero Aldana Paula AHL SCNDR1: So how will he do it? To train the users?
Caballero Aldana Paula AHL SCNDR1: So he asked for our support to do it with the end users.
Caballero Aldana Paula AHL SCNDR1: So that's what we're gonna do, obviously. But just so you know.
Ashmore Matt NRT SCNDR1: I please send me an e-mail with that Paulo Mike has taken a backseat approach for everything. He doesn't make himself available in my opinion.
Ashmore Matt NRT SCNDR1: So I would like to talk to Alan about it, but if you could really write me a formal e-mail and then I can talk to Alan in regards to Mike.
Hadeler Jens FRD SCNDR: So and this this this, what Matt just mentioned, this is very, very much important.
Hadeler Jens FRD SCNDR: So compare it with the delivery process of a baby.
Hadeler Jens FRD SCNDR: The question is who gets the baby?
Hadeler Jens FRD SCNDR: Is that you as a team or is that the plant?
Ashmore Matt NRT SCNDR1: It's clear rights, the plant they're receiving the baby.
Hadeler Jens FRD SCNDR: So and this and this backseat approach doesn't work.
Ashmore Matt NRT SCNDR1: Yeah. And there there's some.
Hadeler Jens FRD SCNDR: So you set example you set example Paula. When you talk to Mike, explain him who gets the baby.
Hadeler Jens FRD SCNDR: So you can assist you can guide.
Hadeler Jens FRD SCNDR: You are like a midwife in this case.
Hadeler Jens FRD SCNDR: You will not get the baby.
Hadeler Jens FRD SCNDR: You will not feel the pain.
Hadeler Jens FRD SCNDR: Mike will feel the pain.
Ashmore Matt NRT SCNDR1: And just for a moment of laughter, Renee, take all this in.
Ashmore Matt NRT SCNDR1: OK, inside joke, nobody laughed.
Ashmore Matt NRT SCNDR1: OK.
Westrup Renee MNR SCNDR1: I I didn't hear.
Hadeler Jens FRD SCNDR: Yeah. I I I like this.
Hadeler Jens FRD SCNDR: I like this.
Hadeler Jens FRD SCNDR: Practical examples.
Hadeler Jens FRD SCNDR: Yeah. And everybody has a picture behind because many of you have children. Not everybody.
Hadeler Jens FRD SCNDR: Yeah. But you know what it means.
Hadeler Jens FRD SCNDR: And there is no discussion if the husband or the wife gets a baby. There is no discussion about that.
Kota Christina AHL SCNDR1: You can't return them.
Hadeler Jens FRD SCNDR: Exactly. And you can't return on.
Hadeler Jens FRD SCNDR: You can't return on.
Hadeler Jens FRD SCNDR: That's the same like SAP implementation. If you have jumped into the water, then it's gone.
Ashmore Matt NRT SCNDR1: Yeah. Christina, you had a comment.
Kota Christina AHL SCNDR1: Yeah. So we just got off a call with David Taggerdine.
Kota Christina AHL SCNDR1: He's got a training plan that him and Christoph feeling have.
Kota Christina AHL SCNDR1: Come up with and he has asked Renee. Paula, myself and Jose.
Kota Christina AHL SCNDR1: To fill in his training matrix with the dates that were available to provide training.
Kota Christina AHL SCNDR1: And they'd like to.
Kota Christina AHL SCNDR1: Be I I mean, at this point David's leading some training.
Kota Christina AHL SCNDR1: But he definitely wants to be a part of him and and his colleague Lauren, they want to be a part of the training and they they also requested the dates that Phillip Heitz and Kasha will be available.
Kota Christina AHL SCNDR1: To get their involvement as well, so I.
Kota Christina AHL SCNDR1: Think there needs to be some alignment I guess.
Ashmore Matt NRT SCNDR1: Yeah. So, so, Christoph and I spoke offline about this training matrix.
Ashmore Matt NRT SCNDR1: I think what he's doing is very good.
Ashmore Matt NRT SCNDR1: I like his approach.
Ashmore Matt NRT SCNDR1: It it should have been the initiative should have been taken by the plant.
Ashmore Matt NRT SCNDR1: But he's taking it from a divisional C perspective, and he wants to ensure that his PES are equipped to support after we deliver the baby so-called, and then we go to the next baby delivery. But.
Ashmore Matt NRT SCNDR1: Yeah, it's not confirmed if if anyone will travel.
Ashmore Matt NRT SCNDR1: Additionally we, we are still.
Ashmore Matt NRT SCNDR1: Ian's are still checking with with Phillip.
Ashmore Matt NRT SCNDR1: But for training, I think it doesn't have to be onsite, right?
Ashmore Matt NRT SCNDR1: Phillip can do some training in, in, in support David, they have a relationship anyway from Division C.
Ashmore Matt NRT SCNDR1: But.
Ashmore Matt NRT SCNDR1: The majority of that training from David in to David and Laura and I think Gabriel is the other gentleman's name, needs to come from us. But I I appreciate Chris's approach.
Hadeler Jens FRD SCNDR: Exactly.
Ashmore Matt NRT SCNDR1: I appreciate christoph's approach and.
Ashmore Matt NRT SCNDR1: It's it's great.
Ashmore Matt NRT SCNDR1: I just think, and I told him, hey, this same initiative should have been taken from the plant.
Ashmore Matt NRT SCNDR1: And he agrees.
Ashmore Matt NRT SCNDR1: But, but you know, he he wasn't at liberty to discuss anything with me in regards to that approach with the plant.
Kota Christina AHL SCNDR1: Yeah. Yes. I'm just getting a little confused on.
Kota Christina AHL SCNDR1: Who is?
Kota Christina AHL SCNDR1: Who is leading what and how that's happening? Cause Paula and I had originally planned to be at the plants on site Wednesday and Friday.
Ashmore Matt NRT SCNDR1: Please continue.
Kota Christina AHL SCNDR1: Dave. Yeah, David's asking that we be there.
Kota Christina AHL SCNDR1: That I be there on Tuesday.
Kota Christina AHL SCNDR1: So I I'm I I agree. I can be there on Tuesday.
Kota Christina AHL SCNDR1: It doesn't matter what day to me. I'll make myself available, but.
Kota Christina AHL SCNDR1: I just wanna make sure that.
Kota Christina AHL SCNDR1: We're aligned on how this training should take place.
Ashmore Matt NRT SCNDR1: So in this instance, we're Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: It's quite chaotic, to be honest. Christoph Billings, running his own way of training.
Ashmore Matt NRT SCNDR1: Karen Mueller is running her own way of training.
Ashmore Matt NRT SCNDR1: Alan's running a separate way of training. It's very chaotic.
Ashmore Matt NRT SCNDR1: There is no true one source and unfortunately I think we have to live with that for today for defense in a fowlerville project. But moving forward, there needs to be something like in place to streamline the process and and in place for Marshall Echo.
Ashmore Matt NRT SCNDR1: But echo, I know we're going to totally, totally different situation. The training has been.
Ashmore Matt NRT SCNDR1: Key users haven't taken the back seat approach.
Ashmore Matt NRT SCNDR1: They've been very active and and have completed the basic trainings that are required in my HR suite. They have been very active in the trainings with the DTA, so.
Ashmore Matt NRT SCNDR1: Moving forward, we need we need a more streamlined approach for training and not having so many different directions here.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: That's just one point of this presentation, but but it's it's a very important point training and like I said, I will go into discussions with the relevant ETA S for Marshall and also for for lamera and ECHO.
Ashmore Matt NRT SCNDR1: The second slide is also additionally as important and this is where we have three different buckets.
Ashmore Matt NRT SCNDR1: Process readiness, data readiness and operational readiness, and we want to highlight the top five critical topics.
Ashmore Matt NRT SCNDR1: For Fenton in Fowlerville, we have these JIRA tickets and and just the E Bomb M bomb discussion in general.
Ashmore Matt NRT SCNDR1: Where we put who is responsible for.
Ashmore Matt NRT SCNDR1: It it's not always us, but in some cases, of course it is us, but we want to highlight what we see as the top five critical topics.
Ashmore Matt NRT SCNDR1: Before UAT, before go live, before whatever milestone or gate we're coming to, we want to highlight those topics here.
Ashmore Matt NRT SCNDR1: But this this is around the the systematic and the the process.
Ashmore Matt NRT SCNDR1: Area. So this is the area for or this is the focus for this.
Ashmore Matt NRT SCNDR1: Process readiness. Then we have the data readiness.
Ashmore Matt NRT SCNDR1: We talked about this multiple times.
Ashmore Matt NRT SCNDR1: The bomb.
Ashmore Matt NRT SCNDR1: Status being below the status 30, the operational bomb having.
Ashmore Matt NRT SCNDR1: Being a complete mess, missing routings, production versions where we are equally as responsible for the production versions.
Ashmore Matt NRT SCNDR1: One CB, which Jan Keller's not here, but Jan Keller and Jorge took care of that topic is now closed.
Ashmore Matt NRT SCNDR1: Just where we didn't have the status, we had the incorrect status previously and then we have the master data and data review where me and Thiago will will support there as well. And then the operational readiness.
Ashmore Matt NRT SCNDR1: Edi readiness. We all know it's issue.
Ashmore Matt NRT SCNDR1: Karen is a front runner and I know that Renee is supporting, but but Karen is the front runner here.
Ashmore Matt NRT SCNDR1: The queues are training for outbound was escalated.
Ashmore Matt NRT SCNDR1: And Paula, here you are responsible.
Ashmore Matt NRT SCNDR1: I know Alice supporting but but here you are responsible.
Ashmore Matt NRT SCNDR1: Key user end user training was highlighted as you've seen on the previous slide, and then we'll talk about missing authorizations still for ZP 1.
Ashmore Matt NRT SCNDR1: So Janine is there responsible from the project perspective?
Ashmore Matt NRT SCNDR1: Paulo, you have a question comment.
Caballero Aldana Paula AHL SCNDR1: 15 yeah, question.
Caballero Aldana Paula AHL SCNDR1: Can we add that the the JIRA that's open for the picking configurations in mobisys because it hasn't been transported yet so we cannot do testing of some of the functions that they implemented?
Ashmore Matt NRT SCNDR1: Yeah, please send me that ticket, OK. And I will also escalate it. 'cause. I sent a a mail already today to Jaime asking for support on several different tickets including these tickets.
Ashmore Matt NRT SCNDR1: But yeah, for sure I can.
Ashmore Matt NRT SCNDR1: We can add something here, but we want to keep it really to the top five.
Ashmore Matt NRT SCNDR1: But that is a critical process, so I will add it.
Ashmore Matt NRT SCNDR1: And this is a living document, guys.
Ashmore Matt NRT SCNDR1: It's not meant to be maintained once and then done.
Ashmore Matt NRT SCNDR1: We need to keep maintaining it and keeping it up to date for all of our projects.
Hadeler Jens FRD SCNDR: So Paula, just to give you an an update, the 13990.
Hadeler Jens FRD SCNDR: Is tested in Zhangjiagang positively.
Hadeler Jens FRD SCNDR: So they were waiting for the same process.
Hadeler Jens FRD SCNDR: So it is available in ZT1, but not maybe not for the plans in Fenton and Fowlerville.
Hadeler Jens FRD SCNDR: So that should be possible to solve by the configuration factory and the from my perspective at least there is no more activity necessary from the build side.
Caballero Aldana Paula AHL SCNDR1: I was told there was one transport already in city one, but there's another one missing, so I'm not sure who has to do that.
Hadeler Jens FRD SCNDR: So but this is this is a good example.
Hadeler Jens FRD SCNDR: Yeah. So we have.
Hadeler Jens FRD SCNDR: Reviewed the JIRA system and there are multiple tickets.
Hadeler Jens FRD SCNDR: Some tickets older than four weeks.
Hadeler Jens FRD SCNDR: In. Yeah, so.
Hadeler Jens FRD SCNDR: And there was a very, very intensive discussion beginning this week.
Hadeler Jens FRD SCNDR: Who is the owner of these tickets?
Hadeler Jens FRD SCNDR: Yeah, the owner is always.
Hadeler Jens FRD SCNDR: The functional responsible.
Hadeler Jens FRD SCNDR: So that means if you have a functional responsibility find like for example.
Hadeler Jens FRD SCNDR: The build P/E when it is a template ticket, then it's clear, but if there is no functional responsibility find then it's automatically the reporter.
Hadeler Jens FRD SCNDR: Yeah, and that means please.
Hadeler Jens FRD SCNDR: Take a look on the tickets you have created and remind people to do what is necessary to do so, the assignees.
Hadeler Jens FRD SCNDR: Yeah. And if that is not done, then don't wait. There is no person coming that is doing the tracking.
Hadeler Jens FRD SCNDR: There is no tracking department or so.
Hadeler Jens FRD SCNDR: Yeah. So please give people a friendly reminder.
Hadeler Jens FRD SCNDR: Yeah, maybe a second one, but don't wait too long. And if that is not working then the project lead in case of Fenton and Fowler will please Fernando.
Hadeler Jens FRD SCNDR: Additionally, yeah, needs to be contacted so the escalation process needs to be started by you, yeah?
Hadeler Jens FRD SCNDR: There is no. As I said, no, no tracking department or so. That is a topic. Help yourself.
Hadeler Jens FRD SCNDR: Yeah, so otherwise nobody will do something here.
Hadeler Jens FRD SCNDR: Push people.
Hadeler Jens FRD SCNDR: Follow them.
Hadeler Jens FRD SCNDR: You don't need to do everything by your own.
Hadeler Jens FRD SCNDR: Sometimes you don't have authorization to do. Sometimes you cannot do.
Hadeler Jens FRD SCNDR: But please help people to do their job.
Hadeler Jens FRD SCNDR: Or remind them.
Hadeler Jens FRD SCNDR: Unfortunately, sometimes the the the topics.
Hadeler Jens FRD SCNDR: Will be done first, where the escalation is the most intensive.
Hadeler Jens FRD SCNDR: Unfortunately, that is the case.
Ashmore Matt NRT SCNDR1: Sometimes you have to babysit the baby.
Hadeler Jens FRD SCNDR: Coming back to the baby example.
Ashmore Matt NRT SCNDR1: Yeah, alright.
Ashmore Matt NRT SCNDR1: Any questions in regards to this?
Ashmore Matt NRT SCNDR1: Again, it's a living document.
Ashmore Matt NRT SCNDR1: It will be on our SharePoint.
Ashmore Matt NRT SCNDR1: There will be one for Fenton Fowlerville.
Ashmore Matt NRT SCNDR1: The only thing I request is as we update it we we change the date. As you see this was communicated on the 11th.
Ashmore Matt NRT SCNDR1: We make updates what? Whatever. We should change the the create a new file and and update the date in the in the description.
Hadeler Jens FRD SCNDR: Yeah, that is only a piece of paper.
Hadeler Jens FRD SCNDR: Yeah. What is important is the visibility. That means we all need to have the visibility and talking about the project of Marshall, yeah, and talking about the training.
Hadeler Jens FRD SCNDR: The topics, the major issues, trainings need to be done prior to integration test and integration test will start soon.
Hadeler Jens FRD SCNDR: Yeah.
Hadeler Jens FRD SCNDR: So that is very, very much important.
Hadeler Jens FRD SCNDR: So and I can take that with me to remind them to do it.
Hadeler Jens FRD SCNDR: Yeah. And if people know how the logic is, they can only do the quiz.
Hadeler Jens FRD SCNDR: But at the end we need to confirm yes, people are knowledgeable and people know how to access.
Hadeler Jens FRD SCNDR: These enable now videos and so on so that they are able to help themselves.
Hadeler Jens FRD SCNDR: I want to know how to book this and that OK before asking a person I can look into the enable now video.
Hadeler Jens FRD SCNDR: Yeah, so this help yourself approach is important.
Ashmore Matt NRT SCNDR1: Cena.
Kota Christina AHL SCNDR1: In regards to the scrap ticket, I had a discussion today with Bugra about training for CO1F.
Kota Christina AHL SCNDR1: Prior to that, I had a discussion with David Tagerdine and Christoph Feelling.
Kota Christina AHL SCNDR1: They are.
Kota Christina AHL SCNDR1: Apparently there was an informal conversation held with Hans Joerg where he said that the interim solution should not be Co 1F.
Kota Christina AHL SCNDR1: It should be using Migo.
Kota Christina AHL SCNDR1: And I'm a little confused at what direction should be followed there now.
Kota Christina AHL SCNDR1: So I don't know if if we can, maybe we need to discuss that offline.
Kota Christina AHL SCNDR1: There seems to be yet again.
Kota Christina AHL SCNDR1: Change in which process should be used?
Ashmore Matt NRT SCNDR1: In, in my opinion, Christina was this was this documented?
Ashmore Matt NRT SCNDR1: Did Christoph give you this in an e-mail or was this verbal?
Kota Christina AHL SCNDR1: It was verbal, but I did just send an e-mail to them saying that.
Kota Christina AHL SCNDR1: Col. CO1F would not be able to be used without the allocation of the Bill of Materials and the routings.
Kota Christina AHL SCNDR1: And.
Kota Christina AHL SCNDR1: They I sent them the emails on the line items.
Kota Christina AHL SCNDR1: For the amount of scrap that's recorded on a daily basis at the plants and I believe they are going to be providing that information to Hanzo Org Voss.
Kota Christina AHL SCNDR1: So I've asked.
Kota Christina AHL SCNDR1: If there is a decision to use Migo that I mean to be honest, using Migo would be a much easier, efficient process for a solution.
Kota Christina AHL SCNDR1: But I'm under the impression that it was not allowed from Andrea Kiefer.
Kota Christina AHL SCNDR1: So if that solution is going to be able to be used if we can get the the final.
Kota Christina AHL SCNDR1: Official permission to use it because I'm at the point now where.
Kota Christina AHL SCNDR1: The the I just need to know which solution we're going to use so that I can provide the training the plants really pushing on, getting training for the process.
Ashmore Matt NRT SCNDR1: Mm hmm mm.
Ashmore Matt NRT SCNDR1: Yeah. Let's talk to David.
Ashmore Matt NRT SCNDR1: They need to write something official and this is their stance and how we shall move forward, whether it's this, the Migo or CO1F, to be honest, I don't care.
Hadeler Jens FRD SCNDR: Yeah. So, but in in, in general, yeah, we don't need to ask David here.
Hadeler Jens FRD SCNDR: Yeah. So David can support with his knowledge about Co 1F and how to deal with it.
Hadeler Jens FRD SCNDR: But we are the roll out team. Yeah and.
Hadeler Jens FRD SCNDR: We should here use the the audio and what we have audience what we have, yeah.
Hadeler Jens FRD SCNDR: So if there and there is an e-mail from earlier today from David Tegardine Matthew and copy, so that was only a selected number of people in copy.
Hadeler Jens FRD SCNDR: Don't know a little bit strange, but OK. It was like it was, yeah.
Hadeler Jens FRD SCNDR: And.
Hadeler Jens FRD SCNDR: With with that example, we should simply use the project team to go forward. Don't ask too much. We have so much pressure here.
Hadeler Jens FRD SCNDR: Yeah, sometimes we have to ask for forgiveness later.
Kota Christina AHL SCNDR1: Yeah, for me.
Hadeler Jens FRD SCNDR: Don't ask too much for permission. If the process works. If it is as interim solution possible to go that way, do it.
Kota Christina AHL SCNDR1: Yeah, I think that's kind of the the problem that I'm having is I I know I know it's been made clear with the raysic who who should decide which process is followed. But I think when.
Kota Christina AHL SCNDR1: We're talking into the at the divisional level. It's handled differently than the Rasic and other people are in charge of it, and now it's.
Ashmore Matt NRT SCNDR1: Yeah, but let's follow the racing at the end of the.
Hadeler Jens FRD SCNDR: Project the project, team the Roll Out Project team needs to deliver.
Hadeler Jens FRD SCNDR: Yeah, and Andrea Kiefer is a GPO.
Hadeler Jens FRD SCNDR: Yeah. So we are talking about a target solution when the GPO is involved for sure that target solutions is scrap cockpit needs to be aligned with her.
Hadeler Jens FRD SCNDR: Yeah, but don't. Don't ask too much. If you ask 20 people, you will get 21 answers.
Kota Christina AHL SCNDR1: I know I didn't ask. I was told today that we should use Migo, and I mean it.
Kota Christina AHL SCNDR1: It was supposedly coming from Hans Joerg Voss.
Kota Christina AHL SCNDR1: Who's our GPO, right? So.
Kota Christina AHL SCNDR1: I don't.
Kota Christina AHL SCNDR1: Oh, I thought.
Kota Christina AHL SCNDR1: He's the GPO for production.
Hadeler Jens FRD SCNDR: Yeah, he's he's both.
Ashmore Matt NRT SCNDR1: He is a both.
Kota Christina AHL SCNDR1: So I I just would like some clarity from the Gpos on which process I need to provide the training on.
Kota Christina AHL SCNDR1: I don't care at this point.
Kota Christina AHL SCNDR1: I don't care which one it is, I just need to know which one to use so that I can move forward in training.
Hadeler Jens FRD SCNDR: So make it do it in a simple way.
Hadeler Jens FRD SCNDR: Yeah, Fenton and Fowlerville project team is defined.
Hadeler Jens FRD SCNDR: You have project meetings.
Hadeler Jens FRD SCNDR: Yeah, in project meetings there is.
Hadeler Jens FRD SCNDR: They are controlling Dtas. There is everybody in.
Hadeler Jens FRD SCNDR: Use.
Hadeler Jens FRD SCNDR: The result out of the the investigation with David say that is our aligned proposal.
Hadeler Jens FRD SCNDR: Can we go forward?
Hadeler Jens FRD SCNDR: Put it into the minutes.
Ashmore Matt NRT SCNDR1: Careful that that we don't take ownership.
Ashmore Matt NRT SCNDR1: That's what I wanna be careful, because then we will remain the owners for every project in the future.
Ashmore Matt NRT SCNDR1: Yeah. Operations is the owner, regardless of what Tay Tay believes there matrix. It was agreed upon in the Royal meetings and the project meetings.
Ashmore Matt NRT SCNDR1: She is the the so-called owner. We were trained however she wants to train.
Ashmore Matt NRT SCNDR1: I don't care, but we would not make the final decision because then we it.
Ashmore Matt NRT SCNDR1: It's not, it's not.
Hadeler Jens FRD SCNDR: That is exactly.
Hadeler Jens FRD SCNDR: And that is the reason why I said use the project team meetings.
Kota Christina AHL SCNDR1: OK.
Hadeler Jens FRD SCNDR: Put it in the minutes and then you have a decision and if somebody is not agreeing to that, then he should do something better.
Ashmore Matt NRT SCNDR1: We have enough crap on our shoes.
Hadeler Jens FRD SCNDR: Proposals are proposals are always impossible, and we, as supply chain, are definitively responsible for the execution, not for the process but for the execution.
Hadeler Jens FRD SCNDR: We are responsible, so that means we feel the pain and if we feel the pain we are we are able to make a proposal.
Hadeler Jens FRD SCNDR: And the audience to make a decision is the project team meeting.
Hadeler Jens FRD SCNDR: Yeah you can.
Hadeler Jens FRD SCNDR: You can ask, as I said, 20 people.
Hadeler Jens FRD SCNDR: You will get 21 answers and opinions.
Ashmore Matt NRT SCNDR1: Yep. Now I wanna take move away from that a little bit.
Ashmore Matt NRT SCNDR1: Any final questions regarding this presentation?
Ashmore Matt NRT SCNDR1: Again, I will work with gash from ECHO and Lamera and Marshall to to to start the initiative for those projects, but any further questions, comments.
Ashmore Matt NRT SCNDR1: If not, then let's I I want to align on our availability and travel for Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: Yesterday I had a call with with Fernando after the 3:00 call or 40 clock call.
Ashmore Matt NRT SCNDR1: Regarding the cutover plan and wanted to inform him that we have a plan in domain S.
Ashmore Matt NRT SCNDR1: This is how we will execute it and and and we are covered. He was OK with that.
Ashmore Matt NRT SCNDR1: But one thing I want to make sure of, I put you two guys in for this travel.
Ashmore Matt NRT SCNDR1: Oh, wait a minute.
Ashmore Matt NRT SCNDR1: Where you, Paula?
Ashmore Matt NRT SCNDR1: Yeah, I put you guys in for these two days because it's what you informed me earlier. If you're going on Tuesday.
Ashmore Matt NRT SCNDR1: Christina please just added here. I can add it doesn't matter, doesn't matter.
Ashmore Matt NRT SCNDR1: But just so that it's, it's documented here and everyone knows everyone knows who's gonna be on site and when they're gonna be on site.
Ashmore Matt NRT SCNDR1: But the the main target of today is the expectation to be there on the 27th or be available that weekend.
Ashmore Matt NRT SCNDR1: Is that clear for everyone?
Kota Christina AHL SCNDR1: We need to be there for from Friday.
Kota Christina AHL SCNDR1: Starting Friday till.
Kota Christina AHL SCNDR1: To.
Kota Christina AHL SCNDR1: Friday on.
Ashmore Matt NRT SCNDR1: 11th of October, yeah.
Hadeler Jens FRD SCNDR: Yeah. So that is that is something what is, I would say, a preparation scenario, yeah.
Hadeler Jens FRD SCNDR: So the decision if the go live will happen on the 1st of October.
Hadeler Jens FRD SCNDR: The decision will be made, I guess, on the 26th.
Ashmore Matt NRT SCNDR1: That's correct.
Hadeler Jens FRD SCNDR: So.
Ashmore Matt NRT SCNDR1: That's why everyone should book this this.
Ashmore Matt NRT SCNDR1: Optional cancellation as well.
Hadeler Jens FRD SCNDR: And I will be. Here's that.
Hadeler Jens FRD SCNDR: What what you see here? I will be travelling to Fenton and Fowlerville, but I will not be there all the time because that includes the flights. What you show here?
Hadeler Jens FRD SCNDR: So that is a little bit misleading.
Hadeler Jens FRD SCNDR: Yeah, but we can correct that afterwards. Yeah, so.
Hadeler Jens FRD SCNDR: So Etsy at the end, what is important. We need to identify everything. What needs to be done.
Hadeler Jens FRD SCNDR: Yeah, so the cutover plan execution is priority.
Hadeler Jens FRD SCNDR: And the visibility it needs to be clear because if the visibility is not there.
Hadeler Jens FRD SCNDR: Yeah. And the SuccessFactors are not clear.
Hadeler Jens FRD SCNDR: Or the risks, then potentially a wrong decision will be made.
Hadeler Jens FRD SCNDR: Yeah. So that is really, really, really important.
Ashmore Matt NRT SCNDR1: But again, we need a plan.
Hadeler Jens FRD SCNDR: Exactly.
Ashmore Matt NRT SCNDR1: To to show that we are prepared and.
Ashmore Matt NRT SCNDR1: This is why the 27th on we need to be prepared to be onsite.
Ashmore Matt NRT SCNDR1: To the 11th of October, as of now. Then we have to discuss what happens. Maybe. Maybe.
Ashmore Matt NRT SCNDR1: At some point, we need to discuss going back home for the weekend, maybe two or three days home and then coming back because it's not in my eyes.
Ashmore Matt NRT SCNDR1: Realistic to ask you to be gone for a month.
Ashmore Matt NRT SCNDR1: Yeah, you need to go back home. See the family for at least a weekend and then come back for those who are local to visit in Fallowfield. I mean, within maybe one to 1 1/2 hours driving. I know it's quite easy, but for those who are.
Ashmore Matt NRT SCNDR1: Traveling, even though we call it domestic Mexico is is still an international flight.
Ashmore Matt NRT SCNDR1: We need to arrange that as well, but is it clear for everyone?
Ashmore Matt NRT SCNDR1: Jose will arrive on the 23rd.
Ashmore Matt NRT SCNDR1: We've already discussed.
Ashmore Matt NRT SCNDR1: I know he had to jump to another call really quick, but.
Ashmore Matt NRT SCNDR1: He's prepared, he's made his flights arrangements and he will arrive on the 23rd and possibly stay for for three weeks.
Ashmore Matt NRT SCNDR1: He's arranging that internally with his family now, but for sure these two weeks.
Ashmore Matt NRT SCNDR1: But possibly the the the October to the October 11th, but for the others.
Ashmore Matt NRT SCNDR1: Make arrangements to be there from the 27th to the 11th please.
Ashmore Matt NRT SCNDR1: Guillermo, I don't know your availability. Or are you still traveling? I know that.
Ashmore Matt NRT SCNDR1: You are not available for UAT, but how does it look for?
Uribe Guillermo MNR FIMS51: Yeah. So it is a little complicated.
Uribe Guillermo MNR FIMS51: I don't think I'll be able to travel, but I can support remotely.
Ashmore Matt NRT SCNDR1: So you will be available during the weekends for remote support there as well, right?
Uribe Guillermo MNR FIMS51: Yeah.
Hadeler Jens FRD SCNDR: Yeah. So one topic, what I would like to mention here, yeah, so.
Hadeler Jens FRD SCNDR: With everything here, don't forget.
Hadeler Jens FRD SCNDR: Don't forget that Marshall integration test will come.
Ashmore Matt NRT SCNDR1: Yes, yes. And and I told you prior that I've had this escalation already with Fernando in the project team.
Ashmore Matt NRT SCNDR1: He fully expects that it will be delayed in Marshall.
Ashmore Matt NRT SCNDR1: He hasn't gave it an official confirmation yet, but it's not possible for the DT as to be in two places at once.
Ashmore Matt NRT SCNDR1: It's just not.
Hadeler Jens FRD SCNDR: Maybe, yeah, but as long as nothing is written, we need to take track here.
Hadeler Jens FRD SCNDR: Yeah, we should.
Hadeler Jens FRD SCNDR: Not completely. Ignore it.
Hadeler Jens FRD SCNDR: Yes, nobody was expecting 3 weeks ago.
Hadeler Jens FRD SCNDR: That's a decision will be made.
Hadeler Jens FRD SCNDR: Fenton and Fowler will have go live on the 1st of October.
Hadeler Jens FRD SCNDR: Nobody, including me.
Hadeler Jens FRD SCNDR: Yeah. So there is a lot of pressure in ZF group.
Hadeler Jens FRD SCNDR: And I don't want to have the pressure exclusively on our shoulders here.
Hadeler Jens FRD SCNDR: Yeah. So for example, if we have Jan and Christina for production, then for sure you need to assist each other.
Hadeler Jens FRD SCNDR: Yeah, but it would make sense that one concentrates on Fenton and Fowlerwillmore and one concentrates more on Marshall.
Hadeler Jens FRD SCNDR: Not to completely ignore it.
Hadeler Jens FRD SCNDR: Yeah.
Ashmore Matt NRT SCNDR1: And this can be an interim step.
Ashmore Matt NRT SCNDR1: Yeah, we've talked and I've talked to Jan.
Ashmore Matt NRT SCNDR1: I think Jan's on the call and I've talked to Christina about this.
Ashmore Matt NRT SCNDR1: We need to discuss it further, but I think that that is an option.
Ashmore Matt NRT SCNDR1: Renee, please include Thiago. More and more he can support. In some ways Marshall as well.
Ashmore Matt NRT SCNDR1: From Ewm, we have the colleagues starting on the 17th again.
Ashmore Matt NRT SCNDR1: Jose, we talked about that, but he's on hold. But we talked about how the support efforts will look there.
Ashmore Matt NRT SCNDR1: So.
Ashmore Matt NRT SCNDR1: Just prepare ourselves.
Ashmore Matt NRT SCNDR1: But but we also have to be realistic here.
Ashmore Matt NRT SCNDR1: It's not possible for us as individuals to be in more than one place at a time.
Ashmore Matt NRT SCNDR1: And it, yeah, I will refrain from saying things that I shouldn't, but.
Hadeler Jens FRD SCNDR: Yeah. So I would.
Hadeler Jens FRD SCNDR: I simply want to say don't ignore Marshall.
Hadeler Jens FRD SCNDR: So I I didn't say put it on priority.
Hadeler Jens FRD SCNDR: I said don't ignore it.
Ashmore Matt NRT SCNDR1: Yes, absolutely. Agreed.
Hadeler Jens FRD SCNDR: Yeah.
Ashmore Matt NRT SCNDR1: OK, OK.
Ashmore Matt NRT SCNDR1: This Tony Troy, I'm not sure about him.
Ashmore Matt NRT SCNDR1: Actually, I haven't heard from him in ages now, but he's not a part of our team directly. I mean, he was. Yeah, yeah, I think.
Kota Christina AHL SCNDR1: Troy, Troy City Fenton so yeah.
Ashmore Matt NRT SCNDR1: Yeah, I know, I know, but.
Ashmore Matt NRT SCNDR1: Let me just strike through it because he's not really.
Kota Christina AHL SCNDR1: He he took a new he took a new job.
Kota Christina AHL SCNDR1: So I don't think he's really involved anymore.
Ashmore Matt NRT SCNDR1: Yeah, he kind of disappeared. Yep.
Hadeler Jens FRD SCNDR: Take him out.
Kota Christina AHL SCNDR1: Yeah, he's. He's over security for QAD now, so he won't.
Kota Christina AHL SCNDR1: Involved
Ashmore Matt NRT SCNDR1: Nevertheless, David has confirmed that he'll be he was there those two days. He will confirm with me at some point where they what he will be there next week in the following weeks. I know he told me.
Ashmore Matt NRT SCNDR1: Three days on site is what he's aiming for.
Ashmore Matt NRT SCNDR1: So as soon as I have the confirmation of him and Laura, I will also add it here or he will add it here.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Everything clear for the travel arrangements. Any questions?
Kota Christina AHL SCNDR1: I guess a question for me, Matt.
Kota Christina AHL SCNDR1: I'm gonna be in Fowlerville next week.
Kota Christina AHL SCNDR1: I'm planning on Tuesday, Wednesday and Friday.
Kota Christina AHL SCNDR1: Would it be OK for me to get the hotel for Tuesday night?
Ashmore Matt NRT SCNDR1: Yeah, sure.
Ashmore Matt NRT SCNDR1: I don't.
Hadeler Jens FRD SCNDR: Sure.
Ashmore Matt NRT SCNDR1: For me, yes, yeah.
Ashmore Matt NRT SCNDR1: So now, Tuesday, Wednesday for you, Christina.
Ashmore Matt NRT SCNDR1: And Friday.
Kota Christina AHL SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: For sure.
Ashmore Matt NRT SCNDR1: Again, I will be there next week, so I will see you, Christina, Paulo, and then I will also be there starting the 30th as well.
Ashmore Matt NRT SCNDR1: I don't know where my name is right now, but.
Ashmore Matt NRT SCNDR1: Here I am.
Ashmore Matt NRT SCNDR1: Then I'll the 30th as well.
Ashmore Matt NRT SCNDR1: Alright guys now.
Ashmore Matt NRT SCNDR1: I want to open it up to you guys to to our, to anyone, any comments, anything you want to address with the group here, please feel free to do so.
Ashmore Matt NRT SCNDR1: And it can also be a complaint session, right?
Ashmore Matt NRT SCNDR1: I mean, I like to hear the feedback from everyone, and I know times are extremely stressful.
Ashmore Matt NRT SCNDR1: I'm starting to look more like Thiago.
Ashmore Matt NRT SCNDR1: I've rubbed my hair off my head.
Ashmore Matt NRT SCNDR1: So when you guys see me, I won't have as much hair anymore.
Ashmore Matt NRT SCNDR1: But.
Ashmore Matt NRT SCNDR1: Yeah, yeah, I like to hear your guys's feedback, so.
Caballero Aldana Paula AHL SCNDR1: Just one question, Matt, can we still update the localization concept for Fenton and Fowlerville?
Caballero Aldana Paula AHL SCNDR1: Just two out of few things that I found out yesterday.
Ashmore Matt NRT SCNDR1: I mean.
Ashmore Matt NRT SCNDR1: Yes, I guess we can.
Ashmore Matt NRT SCNDR1: But we need to create a different version and then we need to align.
Ashmore Matt NRT SCNDR1: Renee, are you on the call?
Hadeler Jens FRD SCNDR: So here Paula one statement at the moment.
Hadeler Jens FRD SCNDR: Yeah, I would say.
Hadeler Jens FRD SCNDR: Execution is more important than documentation in the localization concept, yeah.
Hadeler Jens FRD SCNDR: Yeah.
Caballero Aldana Paula AHL SCNDR1: I just wanna keep track of where we're adding basically.
Hadeler Jens FRD SCNDR: So that that is not a problem, but we didn't.
Hadeler Jens FRD SCNDR: We didn't update the last changes in the localization concept as well, yeah.
Hadeler Jens FRD SCNDR: I would say make a note.
Hadeler Jens FRD SCNDR: Yeah, don't forget it.
Hadeler Jens FRD SCNDR: Mark it in the working version with yellow or however so that you don't forget it.
Hadeler Jens FRD SCNDR: But we don't.
Hadeler Jens FRD SCNDR: We will not spend hours over hours to align with everybody and to discuss with everybody. So at the moment it comes more to execution, back ticket realization and so on.
Hadeler Jens FRD SCNDR: Yeah. So or is that a completely new requirement what you found out?
Caballero Aldana Paula AHL SCNDR1: No, no, it we're just adding more doors.
Caballero Aldana Paula AHL SCNDR1: To.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Yeah, something like this.
Ashmore Matt NRT SCNDR1: Let's make a note.
Ashmore Matt NRT SCNDR1: But it's not doesn't impact the business so much, yeah.
Hadeler Jens FRD SCNDR: Yeah. So and and it's to be honest, as long as it's not conflicting the localization concept, you don't need to have a second version of that.
Caballero Aldana Paula AHL SCNDR1: Thank you. And the other thing?
Caballero Aldana Paula AHL SCNDR1: We sort of found out this week is that.
Caballero Aldana Paula AHL SCNDR1: In order to use wave management, we.
Caballero Aldana Paula AHL SCNDR1: We need the bulk storage location, right?
Caballero Aldana Paula AHL SCNDR1: Which is already set up for Phantom and Fowlerville.
Caballero Aldana Paula AHL SCNDR1: The thing is that for for wave management to work, it has to pick from that location and right now every finish good that's been produced is going directly to FT01.
Caballero Aldana Paula AHL SCNDR1: So the work around right now would be to move all that material from FG01 to BU0.
Caballero Aldana Paula AHL SCNDR1: So that the wave management can work so that way they don't have to create warehouse tasks manually.
Caballero Aldana Paula AHL SCNDR1: Or change the material master data in ewm so that when it comes out of production it gets put directly into BU01.
Caballero Aldana Paula AHL SCNDR1: So I didn't know how we wanted to approach that.
Ashmore Matt NRT SCNDR1: But but the storage type right?
Ashmore Matt NRT SCNDR1: You're talking about storage types now, BU and nfg 01, but FG 01. Why? Why are we now changing to the BU01? Because of this wave management is never at least what's on the radar of me.
Caballero Aldana Paula AHL SCNDR1: Yeah, I raised that to cache like no one ever, like told us how.
Caballero Aldana Paula AHL SCNDR1: Which way management is working?
Caballero Aldana Paula AHL SCNDR1: There are no work instructions.
Caballero Aldana Paula AHL SCNDR1: There is no information and there's conflicting comments everywhere, but if we want to use bulk storage then we we should use wave management and to be honest that would be the easiest way for Fenton and Fowler bill to operate anyways.
Caballero Aldana Paula AHL SCNDR1: So I would suggest we do it.
Caballero Aldana Paula AHL SCNDR1: It's just we would need to do those.
Caballero Aldana Paula AHL SCNDR1: We wanted to decide how to proceed with with how to put the stock there.
Ashmore Matt NRT SCNDR1: Mm hmm.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: I think.
Ashmore Matt NRT SCNDR1: Let's talk a little bit more.
Ashmore Matt NRT SCNDR1: Paula, it will become extremely confusing because also in BU01 they will have, let's say maybe it's BU02, something like that.
Ashmore Matt NRT SCNDR1: But they will also have raw material and finished goods, at least for the high warehouse.
Ashmore Matt NRT SCNDR1: Yeah, they will have this bulk storage concept for both.
Gomes Guilherme Trevisol SRC SCNDR1: Same for ECHO met.
Gomes Guilherme Trevisol SRC SCNDR1: The concept is the same for Yup.
Ashmore Matt NRT SCNDR1: Yeah, but I didn't know for echo. Jorge, sorry.
Ashmore Matt NRT SCNDR1: Are you aware of this wave management?
Ashmore Matt NRT SCNDR1: But they have racks for finished goods. If I'm not wrong.
Stevaux Jorge SRC SCNDR1: Yeah, we have Rex and well, we're packaging like cartoons, big cartoons for export.
Stevaux Jorge SRC SCNDR1: Like Guillermo was saying, the data was migrated instead of FG 01. It came for one or two materials. I don't remember to BU0 for when that happens. When it came to BU. Bu right now is there is one topic BU was met in a way.
Stevaux Jorge SRC SCNDR1: That differentiates the higher level Hu and the lower level Hu.
Stevaux Jorge SRC SCNDR1: So in the ewm depend on the report you will see.
Stevaux Jorge SRC SCNDR1: Did you call him empty?
Stevaux Jorge SRC SCNDR1: I have a ticket that I can send as reference.
Caballero Aldana Paula AHL SCNDR1: Yeah.
Stevaux Jorge SRC SCNDR1: This this caused some confusion, so I we moved two things.
Stevaux Jorge SRC SCNDR1: We moved from BU 04 to FG 01 and that same field was available now and we made the change to the in the mapping right.
Stevaux Jorge SRC SCNDR1: So in the next transport the data it will come FG 01 basically.
Ashmore Matt NRT SCNDR1: Yes. Yeah. But are you aware of this wave management or George?
Stevaux Jorge SRC SCNDR1: But the Third Point B, but the Third Point is that BU is also in the customizing to be able to ship it.
Stevaux Jorge SRC SCNDR1: Even before FG 01.
Stevaux Jorge SRC SCNDR1: So you have the locations to be able to ship it.
Stevaux Jorge SRC SCNDR1: Bu is one of them.
Stevaux Jorge SRC SCNDR1: That is, that is there in case it's needed, yeah.
Stevaux Jorge SRC SCNDR1: What's your question then?
Ashmore Matt NRT SCNDR1: Well, I have a lot of question, but do you know about weight management?
Stevaux Jorge SRC SCNDR1: There were.
Stevaux Jorge SRC SCNDR1: There is what is called.
Stevaux Jorge SRC SCNDR1: I don't know if it's wave manage. It's like a simulation. I've I've heard about it.
Stevaux Jorge SRC SCNDR1: It's like simulating peaking, right?
Stevaux Jorge SRC SCNDR1: Something like this, Paula?
Caballero Aldana Paula AHL SCNDR1: Yeah. So it will propose like in either scenario, if it's rack or bulk, it would propose what to pick basically if it's bulk it would just not suggest that Hu, it would just say the quantity, if it's rack, it would suggest the Hu the problem is right now.
Caballero Aldana Paula AHL SCNDR1: In the in CT1 it's not doing, not doing it automatically, but we're working on fixing that.
Stevaux Jorge SRC SCNDR1: Never tested, Matt. Never heard about it.
Ashmore Matt NRT SCNDR1: For racks or you can still have FG 01.
Ashmore Matt NRT SCNDR1: Hold on. So for for the rack.
Caballero Aldana Paula AHL SCNDR1: Well, that's that's kind of what I wanna see because I remember testing with FG01 and it worked.
Caballero Aldana Paula AHL SCNDR1: But right now it's just set up for BU01.
Ashmore Matt NRT SCNDR1: I think we need to talk to cash a little bit.
Stevaux Jorge SRC SCNDR1: So maybe they can only make a, so maybe they can only add the customizing to include FG 01. Then you don't need to change all the racks or the back to be.
Ashmore Matt NRT SCNDR1: Why can we not use FG 01?
Ashmore Matt NRT SCNDR1: And just treat it as if it.
Caballero Aldana Paula AHL SCNDR1: It wouldn't be bulk because then you wouldn't.
Caballero Aldana Paula AHL SCNDR1: It will tell you what which handling unit to pick. If you do FG01 so.
Caballero Aldana Paula AHL SCNDR1: They will go.
Caballero Aldana Paula AHL SCNDR1: Well, we'll need to go and find that Hu to pick instead of just saying I want to pick this one.
Ashmore Matt NRT SCNDR1: Yeah, but my understanding is all of the storage types are totally customizable.
Ashmore Matt NRT SCNDR1: So then we create FG02 and treat it as its bulk.
Ashmore Matt NRT SCNDR1: I need to understand because I think it creates.
Ashmore Matt NRT SCNDR1: I understand.
Ashmore Matt NRT SCNDR1: You think it would make it simpler for operations to pick any Hu within a bulk storage?
Ashmore Matt NRT SCNDR1: Makes sense to me but but from?
Ashmore Matt NRT SCNDR1: A visual standpoint.
Ashmore Matt NRT SCNDR1: It makes sense that it's all FG 01 FG 02.
Ashmore Matt NRT SCNDR1: Yeah, it makes it easier to manage from from that regard, I think we I need some more clarity here. I'm not.
Ashmore Matt NRT SCNDR1: Yeah. You only mentioned this to me, yeah.
Stevaux Jorge SRC SCNDR1: And that that probably that's maybe they made. Do you allowed situation like policy cause in the template or in the tests that we do with template it's never approached both inbound and outbound and I think that's the reason why we have BU available for shipping as well.
Stevaux Jorge SRC SCNDR1: But I agree with you, Matt that maybe FG 02 would be like an ideal.
Stevaux Jorge SRC SCNDR1: I think that never went to place because yeah, just.
Ashmore Matt NRT SCNDR1: Paula, can you?
Ashmore Matt NRT SCNDR1: Can you put a meeting together with with you, myself and George and Kasha?
Ashmore Matt NRT SCNDR1: Because I really need to understand more.
Ashmore Matt NRT SCNDR1: It's hard for me to make.
Ashmore Matt NRT SCNDR1: AI don't want to make the decision.
Ashmore Matt NRT SCNDR1: We need to make the decision but.
Ashmore Matt NRT SCNDR1: It's kind of hard for me to grasp right now, to be honest.
Caballero Aldana Paula AHL SCNDR1: For casha which 1B or G?
Ashmore Matt NRT SCNDR1: Gee, she's the one that informs you, right?
Ashmore Matt NRT SCNDR1: She's very knowledgeable, so I mean, she can give more more detail.
Caballero Aldana Paula AHL SCNDR1: Geeeeee we were testing today and.
Caballero Aldana Paula AHL SCNDR1: We need to understand the correct setup for for for this wave management in general because for some cases it works. For some cases it doesn't.
Caballero Aldana Paula AHL SCNDR1: It's not only about rack, it's about also the material and the issue so.
Stevaux Jorge SRC SCNDR1: But maybe even FG01 if it's plant specific. Maybe FG 01 combined with your plant could be the way you want it without having at jyoto.
Stevaux Jorge SRC SCNDR1: I don't know.
Stevaux Jorge SRC SCNDR1: I think it's something that we need to learn.
Ashmore Matt NRT SCNDR1: We need we have to. We have to discuss in more detail.
Stevaux Jorge SRC SCNDR1: Maybe they say OK because it's only for this plant afgo one makes like a new strategy or something like only looks for the Bing pick. Anything from the Bing instead of a specific K2, right?
Ashmore Matt NRT SCNDR1: Yeah, let's discuss.
Ashmore Matt NRT SCNDR1: I think that's what we need to do, you know from WM. George, you can do that.
Ashmore Matt NRT SCNDR1: I don't see why it's not possible from ewm.
Ashmore Matt NRT SCNDR1: We did it in our external warehouse in SP1. Yeah, exactly.
Ashmore Matt NRT SCNDR1: Exactly. So I need to understand more because it's yeah.
Ashmore Matt NRT SCNDR1: Let's have the discussion with cashew. I think is the right way, but maybe sooner than later because time is running out.
Caballero Aldana Paula AHL SCNDR1: Are you available tomorrow at 8:00?
Ashmore Matt NRT SCNDR1: I make I make myself available tomorrow, 8:00.
Ashmore Matt NRT SCNDR1: Let me check.
Ashmore Matt NRT SCNDR1: Yeah. Yes, let's do it.
Ashmore Matt NRT SCNDR1: K.
Ashmore Matt NRT SCNDR1: Maybe I go through a really quick inbound side.
Ashmore Matt NRT SCNDR1: Do you have anything Renee, Naomi or Guerrilla? Sorry.
Westrup Renee MNR SCNDR1: Just about I have some questions in regard training that David Turganon wants.
Westrup Renee MNR SCNDR1: What is the expectation there for us like to prepare the the training and the materials or follow up the Enable now process?
Ashmore Matt NRT SCNDR1: Yeah, well, this is my opinion only, but it's not to create some training.
Ashmore Matt NRT SCNDR1: Documentation is to to train them in the system and using the Enable.
Ashmore Matt NRT SCNDR1: Now we have this already available. We don't need to reincreate the recreate the wheel and just use this as our training platform.
Westrup Renee MNR SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Against do you object?
Hadeler Jens FRD SCNDR: Yeah. So in in general, what helps the most?
Hadeler Jens FRD SCNDR: Yeah. So if you transfer yourself into the key user's perspective, yeah, they enter the office in the morning and they have activities to do what they do today with the systems they know.
Hadeler Jens FRD SCNDR: Yeah. So in the future, they will.
Hadeler Jens FRD SCNDR: They will have to do the same.
Hadeler Jens FRD SCNDR: But with different programs with different.
Hadeler Jens FRD SCNDR: Logics and so on.
Hadeler Jens FRD SCNDR: So.
Hadeler Jens FRD SCNDR: The most efficient way of training in that stage. Here we are not talking about basics. We are not talking about.
Hadeler Jens FRD SCNDR: Fundamental topics we are talking about how to enable people to do their job.
Hadeler Jens FRD SCNDR: Yeah. So the most efficient one would be Renee or the whole team.
Hadeler Jens FRD SCNDR: Ask the key user hey, what do you do in which sequence?
Hadeler Jens FRD SCNDR: And then train exactly how they can do this.
Hadeler Jens FRD SCNDR: Yeah, create.
Hadeler Jens FRD SCNDR: A favorite yeah, create a layout.
Hadeler Jens FRD SCNDR: Yeah, put them together so that they can make notes and that will help the most. That will be the most efficient way at this point of time.
Ashmore Matt NRT SCNDR1: I agree with that approach, but for the PES we should follow the Enable now.
Ashmore Matt NRT SCNDR1: So for David's training, Laura's training, Gabriel's training that comes from the Enable now.
Hadeler Jens FRD SCNDR: Yeah.
Ashmore Matt NRT SCNDR1: Yeah. So So what he's saying is, and it and it worked good.
Ashmore Matt NRT SCNDR1: Jan Messeder, I think done this for Shirley. If I'm not wrong, he took a different approach with the key users. Now they tell them he, he says.
Ashmore Matt NRT SCNDR1: Hey, how does your day today activities look?
Ashmore Matt NRT SCNDR1: And then he goes through and trains them. And those specific things, I think that's a great approach and I think we will definitely use that moving forward in Marshall, Echo, Farmington, all of the other sites.
Ashmore Matt NRT SCNDR1: But we also can use that in fit and fowlerville for Keyser training when they need support. When we're on site training them follow go through the day-to-day activities, shipping, receiving.
Ashmore Matt NRT SCNDR1: Scheduling production.
Ashmore Matt NRT SCNDR1: Yeah, let them tell you what they do today.
Ashmore Matt NRT SCNDR1: And then you say, hey, but you will use this transaction and this is how you will use it later, yeah.
Ashmore Matt NRT SCNDR1: And when we moved this.
Stevaux Jorge SRC SCNDR1: And you guys use the the end jointed scenarios for it?
Stevaux Jorge SRC SCNDR1: OK, OK, OK. OK.
Ashmore Matt NRT SCNDR1: Yeah, yeah. They took the backseat approach.
Stevaux Jorge SRC SCNDR1: Yeah. Trying to follow their they.
Stevaux Jorge SRC SCNDR1: Yeah, trying to follow the end joint scenario.
Stevaux Jorge SRC SCNDR1: Maybe it's the one of the quickest ways because you have all these steps there, transactions and.
Hadeler Jens FRD SCNDR: So exactly so in in in every project what we can influence?
Hadeler Jens FRD SCNDR: Yeah, we should do that.
Hadeler Jens FRD SCNDR: But imagine first step. If you have a plant like lemur.
Hadeler Jens FRD SCNDR: Take the example of lemira. People have no clue about SAP.
Hadeler Jens FRD SCNDR: Yeah, they they work with QAD.
Hadeler Jens FRD SCNDR: They go through the enable now through the Enable now videos through the myhr SU trainings.
Hadeler Jens FRD SCNDR: Basic knowledge is there.
Hadeler Jens FRD SCNDR: They know what a movement type is.
Hadeler Jens FRD SCNDR: They know what a plant code is and so on.
Hadeler Jens FRD SCNDR: What a company code is all that basics.
Hadeler Jens FRD SCNDR: Then you do in advance to the integration test.
Hadeler Jens FRD SCNDR: You you do the.
Hadeler Jens FRD SCNDR: The functional test with them together. Then you do the Inter integration test end to end scenario. The user acceptance test and then knowledge is there. But due to this backseat approach we are not in the position in Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: Yes.
Hadeler Jens FRD SCNDR: Yeah, Zed is the problem.
Hadeler Jens FRD SCNDR: Yeah. So, but this is coming into focus now. So we need to be pragmatic and the only pragmatic way is.
Hadeler Jens FRD SCNDR: Do train what they need.
Ashmore Matt NRT SCNDR1: Different approach a little bit.
Ashmore Matt NRT SCNDR1: For Fenton and Fowlerville.
Ashmore Matt NRT SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: Does that answer your question? Thank you.
Westrup Renee MNR SCNDR1: Yes, thank you.
Ashmore Matt NRT SCNDR1: Christina, anything? Yawn. Learn me anything from production?
Kota Christina AHL SCNDR1: I just ask if I could get your help in bringing up the scrap process.
Ashmore Matt NRT SCNDR1: Yes, I will support you, however for sure.
Kota Christina AHL SCNDR1: Thank you.
Ashmore Matt NRT SCNDR1: Jan.
Okal Jan GNL SCNDR1: No.
Ashmore Matt NRT SCNDR1: Hear me? No.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: George, Paula.
Stevaux Jorge SRC SCNDR1: Thanks for asking, man.
Caballero Aldana Paula AHL SCNDR1: Thank you.
Ashmore Matt NRT SCNDR1: One last comment Thiago, be ready.
Ashmore Matt NRT SCNDR1: We have a lot of data validation to do so I'm Fenton and Fowlerville will come.
Ashmore Matt NRT SCNDR1: We need to validate it as soon as possible in the quickest turn around time possible.
Ashmore Matt NRT SCNDR1: One comment for you guys.
Ashmore Matt NRT SCNDR1: I was on a call earlier today in regards to Fenton and Fowlerville about the data accuracy or inaccuracy, bomb accuracy and inaccuracy and so forth and so on.
Ashmore Matt NRT SCNDR1: I think they've established a focus on the PLM side to get the the information.
Ashmore Matt NRT SCNDR1: A little better state in windshield for for it to be migrated. I informed them that we will run our validation at ZP 1P when it's in ZP 1P.
Ashmore Matt NRT SCNDR1: We will take that information, run it through the data validation tool and within minutes we can provide the information that is necessary for a delta load and this is what is being talked about now and in discussed. If we can do a delta load one day after the.
Ashmore Matt NRT SCNDR1: Load or two days after the load in order to have a better situation in regards to data.
Ashmore Matt NRT SCNDR1: So there is a discussion about a delta load.
Ashmore Matt NRT SCNDR1: I think it will happen.
Ashmore Matt NRT SCNDR1: But we need to be prepared validate all data.
Ashmore Matt NRT SCNDR1: You know, we need to somehow Christina ensure that we have production versions for every.
Ashmore Matt NRT SCNDR1: Relevant part.
Ashmore Matt NRT SCNDR1: So so we need to discuss that and and figure out how we can check that in a in a really quick glance, maybe using the validation tool as well.
Kota Christina AHL SCNDR1: Yeah. I just.
Kota Christina AHL SCNDR1: I guess, Jose, I would ask if you can send me the the production version storage locations information. So I have that.
Ashmore Matt NRT SCNDR1: Jan.
Okal Jan GNL SCNDR1: Just one comment regarding some of the setups.
Okal Jan GNL SCNDR1: For the printouts.
Okal Jan GNL SCNDR1: From today's discussion, I found out we might not have access or the nobody from the plant could have access to the tables like VB6160 to 63PP control on all yes so.
Okal Jan GNL SCNDR1: I.
Okal Jan GNL SCNDR1: I thought we will at least get somethings but the IT might be big problem, not even look at. It looks to me like we will not even have able to be look at it.
Okal Jan GNL SCNDR1: What is in there in ZP 1?
Ashmore Matt NRT SCNDR1: Yeah. So, So what we we discussed that and and at least for Finn and Fadlerville as of right now, we have a plan to utilize it to to validate and verify our our output conditions. But I don't know if there will be access to to to the to.
Okal Jan GNL SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: So, but that's really not I can.
Ashmore Matt NRT SCNDR1: I can only escalate the topic is not a decision of ours if they should or shouldn't.
Ashmore Matt NRT SCNDR1: I make a suggestion and that's nothing more.
Hadeler Jens FRD SCNDR: So in general general statement regarding ZP 1 excess, yeah, so.
Hadeler Jens FRD SCNDR: Nobody of us here is allowed to do productive postings in ZP 1.
Hadeler Jens FRD SCNDR: Yeah.
Ashmore Matt NRT SCNDR1: Back.
Hadeler Jens FRD SCNDR: That is clear. Yeah, but.
Hadeler Jens FRD SCNDR: We all should have a look.
Hadeler Jens FRD SCNDR: Very very close.
Hadeler Jens FRD SCNDR: Look on what is going on there so view access.
Hadeler Jens FRD SCNDR: Should not be a problem. Yeah, and.
Hadeler Jens FRD SCNDR: Exactly, yeah.
Hadeler Jens FRD SCNDR: And even if we need to to have SE16 access.
Hadeler Jens FRD SCNDR: With the.
Hadeler Jens FRD SCNDR: With the DTA role, all roles should be timely limited.
Hadeler Jens FRD SCNDR: Yeah. So after one year after half a year or whatever, you should not have any excess to the plant in ZP 1.
Hadeler Jens FRD SCNDR: Yeah, but limited access, timely and view only must be possible. Without that, you cannot do your job.
Kota Christina AHL SCNDR1: I would just like to say in in regards to that, on the cutover plan it has.
Kota Christina AHL SCNDR1: For me to create.
Kota Christina AHL SCNDR1: The conditions of the control table and the output conditions.
Kota Christina AHL SCNDR1: So I brought it up that if I have display only access I I clearly can't do that.
Ashmore Matt NRT SCNDR1: Verify that it's done, not actually do it in ZP one. That's a part of the configuration team.
Kota Christina AHL SCNDR1: OK.
Ashmore Matt NRT SCNDR1: So your role is validation, not more.
Ashmore Matt NRT SCNDR1: We shouldn't make any changes directly in ZP 1.
Kota Christina AHL SCNDR1: OK. 'cause, we aligned with Norbert and Paulo, who I guess would maybe be more on the configuration side and they they asked you're gonna do this right?
Kota Christina AHL SCNDR1: And I said, yeah, 'cause at the time I thought I would be able to, but now I'm not able to so.
Hadeler Jens FRD SCNDR: Yeah. So the most important repeat my statement productive postings.
Hadeler Jens FRD SCNDR: In productive in, in ZP one should not be done by anybody of you.
Hadeler Jens FRD SCNDR: Yeah, but everything else, even if you have access to do it, you should not do it.
Hadeler Jens FRD SCNDR: Yeah, that should be done by the plant users by the end users by the key users.
Hadeler Jens FRD SCNDR: But you need to assist them.
Hadeler Jens FRD SCNDR: You need to know that and even if you need to.
Hadeler Jens FRD SCNDR: Helps them correcting the cookie after go live.
Hadeler Jens FRD SCNDR: You need to have access.
Hadeler Jens FRD SCNDR: Without that, it's not possible.
Kota Christina AHL SCNDR1: But I think these topics that me and Jan are having an issue with are more configuration and it's more it related.
Kota Christina AHL SCNDR1: So I don't think even the plant key users will have access.
Okal Jan GNL SCNDR1: But we would at least need to have a view access to to be able to check, yeah.
Hadeler Jens FRD SCNDR: Everything to everything.
Okal Jan GNL SCNDR1: Yes.
Okal Jan GNL SCNDR1: I don't think it's available at this moment, but yeah, something to be look at at the next in next few weeks.
Ashmore Matt NRT SCNDR1: To be completely transparent with you, Jan, I don't think any roles are completely developed yet for ZP 1. Even the end user roles to be honest.
Giron Jorge MNR SCNDR1: Have a similar situation because I need to set up some configuration regarding the Po Bearbeerg printouts and now I'm confused because I receive an instruction from them that I have to do it well the DTA. But now I'm confused.
Giron Jorge MNR SCNDR1: Should I make the configuration set P1?
Giron Jorge MNR SCNDR1: Should I request to it?
Hadeler Jens FRD SCNDR: So you are not configuration factory.
Giron Jorge MNR SCNDR1: OK, understood. Clear.
Ashmore Matt NRT SCNDR1: Alright guys, anything else?
Hadeler Jens FRD SCNDR: I have only one question to Tiago.
Hadeler Jens FRD SCNDR: So we talked a lot about the updates of findings with your phyton tool and everything what was possible and allowed with MMM 17 I guess was executed right.
Araujo Thiago GCR SCNDR1: Yes, we we don't see P1, right?
Hadeler Jens FRD SCNDR: Yeah. So the question is exactly so the question is, what did we do?
Hadeler Jens FRD SCNDR: What did you do with those fields with those issues that have global fields?
Hadeler Jens FRD SCNDR: And were not possible to update with Zed logic.
Ashmore Matt NRT SCNDR1: Yes. So we created some some workflows and.
Ashmore Matt NRT SCNDR1: Uh.
Ashmore Matt NRT SCNDR1: In the MDG workflow.
Ashmore Matt NRT SCNDR1: Umm.
Ashmore Matt NRT SCNDR1: Yeah, the MDG workflow and only for the parts necessary for UAT at the time.
Ashmore Matt NRT SCNDR1: But what we did to circumvent the problem from happening.
Ashmore Matt NRT SCNDR1: And I'm sure I'm speaking for everyone because I got the confirmation. But what we did to circumvent that for go live is we went back to the D models and and we made the corrections.
Ashmore Matt NRT SCNDR1: Team.
Giron Jorge MNR SCNDR1: As we did and as underst
Araujo Thiago GCR SCNDR1: For the zip one, we can do the same check, but we can change it at 70 like somebody in the plains will have to do it. If you find something.
Ashmore Matt NRT SCNDR1: It will do the same.
Ashmore Matt NRT SCNDR1: Check now discussion is if it will be done via delta load or done via the business.
Ashmore Matt NRT SCNDR1: So we will still do the same.
Ashmore Matt NRT SCNDR1: Check MMM 17.
Ashmore Matt NRT SCNDR1: Not an option.
Ashmore Matt NRT SCNDR1: We will not do that.
Ashmore Matt NRT SCNDR1: Now the discussion is if it is manageable amount of data correction. If the key users will do it or if the number is is, is is far too many for the key users, then the delta load by the MDG or DMF yeah.
Giron Jorge MNR SCNDR1: And it's and it's not. Oh, sorry.
Stevaux Jorge SRC SCNDR1: Then maybe we need as well like SE16N table, at least for division for reviewing, not for changing tables or something.
Ashmore Matt NRT SCNDR1: Exactly. Yeah, I agree.
Ashmore Matt NRT SCNDR1: Yep, you mean in ZP 1?
Ashmore Matt NRT SCNDR1: Yes, I want the same roles that we have now, but just view, that's what my request was.
Stevaux Jorge SRC SCNDR1: No problem.
Ashmore Matt NRT SCNDR1: Take the DTO DTA role now and just make it view access only for ZP 1.
Stevaux Jorge SRC SCNDR1: For the key users having AC16 and for example that's that's great for them too. Just for visualization purpose.
Ashmore Matt NRT SCNDR1: Yeah, yeah.
Ashmore Matt NRT SCNDR1: Yes. Did that answer your question or were you looking for something else or?
Ashmore Matt NRT SCNDR1: Yeah.
Hadeler Jens FRD SCNDR: Yeah. For me, it's important that we take the benefit out of that analysis and not only for the user acceptance test, because that was one time.
Hadeler Jens FRD SCNDR: Yeah. So we need to.
Hadeler Jens FRD SCNDR: We need to use that for the go live as well.
Ashmore Matt NRT SCNDR1: Absolutely.
Hadeler Jens FRD SCNDR: OK.
Ashmore Matt NRT SCNDR1: Thanks guys.
Ashmore Matt NRT SCNDR1: Oh, sorry, go ahead.
Ashmore Matt NRT SCNDR1: Might have had something or.
Ashmore Matt NRT SCNDR1: If not, thank you guys.
Ashmore Matt NRT SCNDR1: And we'll speak soon.
Segura Jose Angel MNR SCNDR1: Yeah. Thank you. Thank you.
Giron Jorge MNR SCNDR1: Good day. Bye bye bye.
