[{"description": "treehash per file", "signed_content": {"payload": "eyJjb250ZW50X2hhc2hlcyI6W3siYmxvY2tfc2l6ZSI6NDA5NiwiZGlnZXN0Ijoic2hhMjU2IiwiZmlsZXMiOlt7InBhdGgiOiJsaXN0LnR4dCIsInJvb3RfaGFzaCI6Il91RFRkOUV6UHdKZUwzanU1Yk5yeTk0aTFwRHFGTzNkXzBxUnYtYVRsS0kifV0sImZvcm1hdCI6InRyZWVoYXNoIiwiaGFzaF9ibG9ja19zaXplIjo0MDk2fV0sIml0ZW1faWQiOiJiZnBnZWRlYWFpYnBvaWRsZGhqY2tuZWthaGJpa25jYiIsIml0ZW1fdmVyc2lvbiI6IjEuMC4xMzk2MSIsInByb3RvY29sX3ZlcnNpb24iOjF9", "signatures": [{"protected": "eyJhbGciOiJSUzI1NiJ9", "header": {"kid": "webstore"}, "signature": "Ss_x_jwahQN-NM84nXZNylaZ2t7MWrmj006ikND-QY1Uk1diC1Qzb3Y50a7YgtI3wTCCTvlqttyg26Oa_119BnC3hzASlnj0pPOY8-r1brWqHbzTd7fNSjexH-w83tcX7qyMNq-QGRlDg_OrFO4P1mxaeiB3T_30PZFIgRl-nXhCu4c3IioAg7wgxKxEHj7N97G6trjwfu_N-j0gytx3gycg3gxYWCxgMC7sPTlkHN4IBNbj6mcxK3iY8rC4Ndblux5-ARKgeWnBE7F86FR2gt-56p7x1X7uNxydnQ5gpY2BOMkcXaGjP_jFkplOUj9e-xa-OZPHLsHiqJZeNEMvXg"}]}}]