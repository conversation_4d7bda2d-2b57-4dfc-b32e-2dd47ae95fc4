<PERSON><PERSON><PERSON> Jan SCW SCNDR: Let's start slowly.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: I would say <PERSON><PERSON>, can you see my screen?
<PERSON><PERSON><PERSON> Jan SCW SCNDR: OK. Perfect.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: Perfect.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: Umm, so then?
<PERSON><PERSON><PERSON> Jan SCW SCNDR: Uh, welcome to the next training session in internal warehousing.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: I would say umm, we can look over short agenda.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: This was just for <PERSON> the last time.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: It's not important and today's agenda.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: I received am a bit of feedback out of Asia Pacific and also America and and EMEA.
Me<PERSON><PERSON> Jan SCW SCNDR: That it was them.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: Umm, yeah.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: Quite comfortable and OK to start with some some easier topics in in general topics, but nevertheless the last meeting was a lot of general information.
Mesche<PERSON> Jan SCW SCNDR: I think this is also quite good because we always have some new people in the project and so on.
Me<PERSON><PERSON> Jan SCW SCNDR: But I would say and that we jump into the system or more into the system for today.
<PERSON><PERSON><PERSON> Jan SCW SCNDR: For today I would, yeah, start with a kind of easy easy transaction or easy tool.
Me<PERSON><PERSON> Jan SCW SCNDR: It's the warehouse monitor and then also jump over to <PERSON><PERSON><PERSON> transaction because this is also very interesting and also needed for especially for the integration tests and so on or when you have some trainings with the key user and at the end we will also talk about, yeah, some, some future topics if there are some questions or some current topics you have issues with and so on.
Meschede Jan SCW SCNDR: So we try to give you also some some space to talk about your own problems or clarify your own questions.
Meschede Jan SCW SCNDR: So yeah, I would say this is quite easy and relaxing and session for today, but nevertheless very, very important and very useful for the daily daily business and daily tasks.
Meschede Jan SCW SCNDR: With view on the on the warehousing,.
Meschede Jan SCW SCNDR: So as I said, the last meeting we did talk about some Q test stuff.
Meschede Jan SCW SCNDR: Uh.
Meschede Jan SCW SCNDR: A bit about some responsibilities and in different streams about yeah, the whole structure would all the processes and so on and UMI noted.
Meschede Jan SCW SCNDR: Uh, out of the last time that it's maybe or that it maybe would be nice to see some kind of transactions that we can use therefore I umm, I searched for some things and I have a yeah, some things that I noted down over the last weeks, months and so on.
Meschede Jan SCW SCNDR: I be honest, I do not maintain them.
Meschede Jan SCW SCNDR: Yeah, every week or every time, but at least these are a kind of few transactions.
Meschede Jan SCW SCNDR: As you can see here, especially for EWM where you can work with or what is kind of important for our stream.
Meschede Jan SCW SCNDR: So at the end I would say I try to copy this and send it to you in the chat so that you can have a look on it and maybe if you like you can also save it.
Meschede Jan SCW SCNDR: Then for you, we have the same.
Meschede Jan SCW SCNDR: Umm for some some warehouse management transaction and UM yeah, I would say this is quite nice to to have a kind of a a small overview here about a view transactions and also a description be honest I need to translate them.
Meschede Jan SCW SCNDR: They are in Germany at the moment.
Meschede Jan SCW SCNDR: That's not that smart, because everything we do in the project is in at least in English and so umm, but I tried to, UM or I will translate them.
Meschede Jan SCW SCNDR: UM, the description field and then send it to you, so maybe this is a kind of a a good help for the beginning.
Meschede Jan SCW SCNDR: Umm, OK.
Meschede Jan SCW SCNDR: So that was one of the questions I I noted from the last meeting.
Meschede Jan SCW SCNDR: Are there any any questions at the moment regarding the last meeting?
Meschede Jan SCW SCNDR: So a few of you did not participate or could not participate.
Meschede Jan SCW SCNDR: But are there questions regarding SharePoint process master list?
Meschede Jan SCW SCNDR: Any other general topic we did spoke about?
Segura Jose Angel MNR SCNDR1: Umm.
Meschede Jan SCW SCNDR: Along.
Meschede Jan SCW SCNDR: Umm.
Meschede Jan SCW SCNDR: If if not, that's also completely OK for me.
Meschede Jan SCW SCNDR: So you could add every time there you can just write me in the chat.
Meschede Jan SCW SCNDR: Write me an email or use the the teams chat here or teams channel chat, whatever to ask some questions.
Meschede Jan SCW SCNDR: So maybe also when I will not answer directly, maybe some of the colleagues who got information or solution.
Meschede Jan SCW SCNDR: So if if there are come something to your mind or some questions popping up, then just interrupt me or write in the chat or anything.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: I would say that we can start then now with the system I will share.
Meschede Jan SCW SCNDR: Again, don't don't.
Meschede Jan SCW SCNDR: Umm yes, and start with the warehousing, monitor.
Meschede Jan SCW SCNDR: I would say for the beginning, because it's a it's a yeah, the actual system we are working mostly with is the set one.
Meschede Jan SCW SCNDR: I guess it's also the set one for you during the integration tests, so we can use this system for example.
Meschede Jan SCW SCNDR: OK, good.
Meschede Jan SCW SCNDR: We can.
Meschede Jan SCW SCNDR: Yeah, let it transaction then sometimes you can see when I forgot about something then you can see the transactions here.
Meschede Jan SCW SCNDR: And please, if you are or you want to know something or need more details or anything or I'm too slow or too fast, please interrupt me and and ask something.
Meschede Jan SCW SCNDR: OK, so as I said for today's session, we want to start with the with the warehousing, monitor.
Meschede Jan SCW SCNDR: Therefore, as this is Edwin Edwin transactions, we need N SCWM and monfore monitor and press enter.
Meschede Jan SCW SCNDR: And as you can see here you need some some fields to to fill up.
Meschede Jan SCW SCNDR: So this is always the the warehouse number.
Meschede Jan SCW SCNDR: So we could choose something else here.
Meschede Jan SCW SCNDR: Maybe there are also your plans, or there should be your plans.
Meschede Jan SCW SCNDR: You have the integration tests, but for us we are working with Shirley because I maintained the most of the material things there because I am responsible for warehousing, in in Shirley for the monitor, it's always SAP standard we use that also in the template.
Meschede Jan SCW SCNDR: So as you can see now here the folder structure is is always the same.
Meschede Jan SCW SCNDR: So this is quite nice then and you don't need to.
Meschede Jan SCW SCNDR: Umm uh, yeah.
Meschede Jan SCW SCNDR: Change your your UM, yeah.
Meschede Jan SCW SCNDR: View on it or or so on, so it's always the same.
Meschede Jan SCW SCNDR: Umm it's, I said you need for S.
Meschede Jan SCW SCNDR: Uh for for EWM transaction.
Meschede Jan SCW SCNDR: Always NSC WM and then the transaction code.
Meschede Jan SCW SCNDR: This is different for some for some MMM or warm transactions.
Meschede Jan SCW SCNDR: So when you for example use the MMBE so kind of an overview here for the stock then umm we don't need it, we just type in the transaction code and then the transaction opens.
Meschede Jan SCW SCNDR: But for today we are oops using the warehousing, monitor.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: And uh, as you can see here, this is UM build up or or structured with kind of different folders.
Meschede Jan SCW SCNDR: As you can see here, there are already separated regarding some kind of streams or areas and that is quite nice because when we open this this folder structure you can see this is uh uh seems like limited.
Meschede Jan SCW SCNDR: Uh. Limitless.
Meschede Jan SCW SCNDR: Uh, uh.
Meschede Jan SCW SCNDR: Folders, but this is very very helpful, especially at the beginning because everything is described.
Meschede Jan SCW SCNDR: So it's very, yeah, very easy pair pair.
Meschede Jan SCW SCNDR: Kind of when you're not completely sure, click and search.
Meschede Jan SCW SCNDR: I would say to to go your way through the system.
Meschede Jan SCW SCNDR: Umm so for example here when we look at these folders and we want to see something, we can always not on the orange ones here, but on the kind of yeah transparent folders here we can always double click on them and when we double click on something for example here the storage bin then a new window opens up and you always have here kind of descriptions and where you can define some some specific values.
Meschede Jan SCW SCNDR: Which you want to search about.
Meschede Jan SCW SCNDR: So for example, here we open storage bin, but you can also when you don't have the the.
Meschede Jan SCW SCNDR: Storage bin you search for.
Meschede Jan SCW SCNDR: You could also go the way we a storage type or section or whatever here.
Meschede Jan SCW SCNDR: So when you open this different folders you always got a kind of different overview and different.
Meschede Jan SCW SCNDR: I would say search possibilities, so this is quite nice.
Meschede Jan SCW SCNDR: So when you have just any kind of information, you want special storage bin, but just have the material number, then you can search via for example the product number or material number or when you search for material number and just have the storage bin you can do it the opposite way.
Meschede Jan SCW SCNDR: So this is quite helpful in the warehouse.
Meschede Jan SCW SCNDR: So when we when we will have a look here do we can yeah just execute it and then it loads for a second but then available stock it shows us any available stock we have in the warehouse at the moment doesn't matter which storage type or storage bin or product number and so on even the status doesn't matter it just show everything that is available at the moment.
Meschede Jan SCW SCNDR: So when we double click on it again and say hey, we want to search for specific material number and execute it or press F8 again then we can see hey we have this material on this storage types and this storage bins available at the moment.
Meschede Jan SCW SCNDR: So for example, this is and new new window that pops up here at the moment.
Meschede Jan SCW SCNDR: So we also have, umm, a lot of information now with these lines here so we can scroll to the to the right.
Meschede Jan SCW SCNDR: I would say I give a short explanation to everything.
Meschede Jan SCW SCNDR: So as you can see here on the on the line above, there's always a kind of a short description.
Meschede Jan SCW SCNDR: So for example, here the storage type is the, the is the area where the material is in the storage bin is the.
Meschede Jan SCW SCNDR: Smallest available area in the in the warehouse.
Meschede Jan SCW SCNDR: So for example it is.
Meschede Jan SCW SCNDR: And one one single space.
Meschede Jan SCW SCNDR: So for example you have here PSA001PSA002PSA003, so these are the smallest sections within a warehouse area.
Meschede Jan SCW SCNDR: When you see it here with a line under the shown, the shown value here or numbers here, then it means that you can always click on it and then use site opens when you press back then you are back again.
Meschede Jan SCW SCNDR: At a general overview, but you can see here when we press the storage bin here, then we got a lot of informations regarding this single storage bin and this is only one.
Meschede Jan SCW SCNDR: For example, one single single box or so on, so you can see here the storage bin type so that for example here everything is allowed and so there are also umm you can also define some special things for this uh storage bin, but all allowed is more useful, especially when you do some some testing things later in the in the productive system.
Meschede Jan SCW SCNDR: This could make more sense that it's, for example something regarding weight or and and the the size of some some product or even the the.
Meschede Jan SCW SCNDR: How can I say that if if something is for example liquid and so on is not allowed to store so you can define a lot of things regarding the the storage bin stage.
Meschede Jan SCW SCNDR: And yeah, you also can see what's laying on this storage bin at the moment.
Meschede Jan SCW SCNDR: So this is the product number, product description and so on.
Meschede Jan SCW SCNDR: Also the batch and yeah, you could also look in a kind of history if there was a there was a what for example different materials laid on this storage bin or also can look which or if there was a kind of inventory counting and so on.
Meschede Jan SCW SCNDR: So you can.
Meschede Jan SCW SCNDR: See.
Meschede Jan SCW SCNDR: See a lot of informations here.
Meschede Jan SCW SCNDR: OK, let's go back.
Meschede Jan SCW SCNDR: The next thing is the product here.
Meschede Jan SCW SCNDR: UM, product uh is just the material or the however you can see it or it's defined with this with this single number here.
Meschede Jan SCW SCNDR: As you can see, there's a line under it, so you also can click on it and what opens here?
Meschede Jan SCW SCNDR: Now you cannot see it in the in the transaction code.
Meschede Jan SCW SCNDR: Really good.
Meschede Jan SCW SCNDR: But it's the material master data, so it means it's the mud one.
Meschede Jan SCW SCNDR: So we have a lot of information not regarding the storage bin this time it is the material and we can also define a lot of things here.
Meschede Jan SCW SCNDR: So for example, for us, very important is for example the warehouse data.
Meschede Jan SCW SCNDR: When you look at the warehouse data, you see that, for example, there's the putaway control defined and storage section indicator.
Meschede Jan SCW SCNDR: So this for example is a kind of a low runner material.
Meschede Jan SCW SCNDR: Umm.
Meschede Jan SCW SCNDR: And for rec storage plan.
Meschede Jan SCW SCNDR: So when you have a A put away, it automatically would send this material for example from the goods receipt zone to direct storage.
Meschede Jan SCW SCNDR: When you click on this?
Meschede Jan SCW SCNDR: Umm button on the on the left side is the the four help so you can also or always see a short description when these short short terms here are not that.
Meschede Jan SCW SCNDR: Umm yeah, family of for you.
Meschede Jan SCW SCNDR: So you can always umm see what?
Meschede Jan SCW SCNDR: Uh SB, for example, means and then uh small boxes.
Meschede Jan SCW SCNDR: Makes sense, but sometimes with all the thousands short terms, it's a bit difficult.
Meschede Jan SCW SCNDR: But yeah, this might.
Meschede Jan SCW SCNDR: One is yeah, to define some.
Meschede Jan SCW SCNDR: Some, yeah, kind of.
Meschede Jan SCW SCNDR: Special things or strategies for a material.
Meschede Jan SCW SCNDR: And when we have a look at this here, you can save this for a second in in your mind.
Meschede Jan SCW SCNDR: And so we open a new window and for example, we can also use this directly as a transaction in EWM.
Meschede Jan SCW SCNDR: As you remember, N slash SCWM mod one and now we have for example this product.
Meschede Jan SCW SCNDR: Here we want to display it only and not change anything.
Meschede Jan SCW SCNDR: But as you can see here, these are the same things.
Meschede Jan SCW SCNDR: You can also see here aren't supposed the same?
Meschede Jan SCW SCNDR: Umm, so this is just the mud one transaction where you can define some some master data on EWM side.
Meschede Jan SCW SCNDR: When you you can close this here.
Meschede Jan SCW SCNDR: Yes, yes, sure.
Orth Christian EXT Westhouse: Yeah, it my may I ask a question between both the yeah about this storage bin umm structure and let's say I mean we are going to discuss them with the key users, maybe not the current project but in the upcoming projects is there kind of a the minimum structure we need to to bear care or is it completely hmm.
Orth Christian EXT Westhouse: What the key user want to have for their plans or how is this approach for this man?
Orth Christian EXT Westhouse: You you did.
Orth Christian EXT Westhouse: You got the question so.
Meschede Jan SCW SCNDR: Yeah, I think I I think I got the question.
Meschede Jan SCW SCNDR: That's a very good question.
Meschede Jan SCW SCNDR: Be honest.
Meschede Jan SCW SCNDR: Sorry that I spent not time on that yet, so when we look at the structure in the warehouse and we say, hey, we have here storage types and storage bins, then I would say for most of the areas.
Meschede Jan SCW SCNDR: So for example, storage type in storage bin, we need everything in the system what we have physically.
Meschede Jan SCW SCNDR: So for example, a rack storage or a bulk storage.
Meschede Jan SCW SCNDR: So imagine you have an area on the ground and you have the RR 010101 then 020304 and you have there for example 6 storage bins physically.
Meschede Jan SCW SCNDR: Then you always need them also in the in the system.
Meschede Jan SCW SCNDR: I'm talking now, not for the testing system that maybe we just need one them to test.
Meschede Jan SCW SCNDR: Have you booked something there and put away on The Reg storage and then and I don't know, book it to production so on that doesn't matter.
Meschede Jan SCW SCNDR: But what we also need additionally is kind of fictive storage areas and at least on EWM side, you then need minimum one storage bin.
Meschede Jan SCW SCNDR: So when we when we look at at the storage bins, we can, yeah, define everything for that.
Meschede Jan SCW SCNDR: So common ways it is only allowed for material for a storage bin to store their one material and when you want to put away the same material on this single storage bin, then at least it should have the same batch number.
Meschede Jan SCW SCNDR: O batch numbers is just quick context.
Meschede Jan SCW SCNDR: Batch numbers are for example, when you have a special time period, you produce some materials and I for example the time period is one week and ingredients you produce with or components you produce with.
Meschede Jan SCW SCNDR: Lasts this one week.
Meschede Jan SCW SCNDR: Then, for example, this one week have could have the same batch number and then for the next week different kind of or same ingredients but a different delivery so could be different than for the next week it's it's a different batch number, but you could also do it kind of like a more detailed way for I don't know shift one, shift two, shift three and every shift when they quit they're working, working shift.
Meschede Jan SCW SCNDR: Then the next batch number for the next shift begins.
Meschede Jan SCW SCNDR: So but yeah, for this fictive ways, maybe we can correctly look into a localization concept.
Meschede Jan SCW SCNDR: Maybe this makes more sense than umm because I am afraid of losing the the red line regarding the question but.
Meschede Jan SCW SCNDR: Yeah, no worries.
Meschede Jan SCW SCNDR: No worries.
Meschede Jan SCW SCNDR: This is a good question and kind of kind of simple question, but the a lot lot around, so let's go to navigation and let's go to.
Orth Christian EXT Westhouse: That's just how we came to a structure in a plant.
Orth Christian EXT Westhouse: So this is a problem that side you know.
Meschede Jan SCW SCNDR: So at the beginning you have the, the, the the storage locations.
Meschede Jan SCW SCNDR: So when you when you look at them, so yeah, these are these are all the all the all the social cases locations we have in in, in, in Shirley for example.
Meschede Jan SCW SCNDR: But when we when we go more down to to storage types then it's.
Meschede Jan SCW SCNDR: I think it's a bit small.
Meschede Jan SCW SCNDR: Is it big enough for you?
Meschede Jan SCW SCNDR: More or less you cannot see it, right?
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: Yeah.
Meschede Jan SCW SCNDR: So am we need to define every single area we need for the plant.
Meschede Jan SCW SCNDR: So for example, we say, hey, we have a we book a goods.
Meschede Jan SCW SCNDR: So we have the the inbound area, OK.
Meschede Jan SCW SCNDR: So we have for example the GR 01 as an area, but this maybe is not.
Meschede Jan SCW SCNDR: I mean, it's physically because we we book a goods receipt and then put the material in this goods receipt area, but it has no it is not maybe separated regarding some storage bins, but at least we would need one storage bin, the goods receipt storage bin for example, to have a kind of fictive storage bin and therefore we could allow umm.
Meschede Jan SCW SCNDR: They are all overalls.
Meschede Jan SCW SCNDR: Storage.
Meschede Jan SCW SCNDR: I don't know how to describe it, so doesn't matter which material or how much doesn't matter weight or or size doesn't matter a batch number and so on.
Meschede Jan SCW SCNDR: So nothing of this matters.
Meschede Jan SCW SCNDR: You could book 1000 different parts on this one storage bin because it's just just a fictive storage bin.
Meschede Jan SCW SCNDR: So yeah, and for the for the rest, I mean it's it's very hard work because you need to look at all the processes.
Meschede Jan SCW SCNDR: Hey, do we need a rack storage or do we have it physically?
Meschede Jan SCW SCNDR: Yes, OK.
Meschede Jan SCW SCNDR: We need, then do we do we fulfill the kind of quality check?
Meschede Jan SCW SCNDR: OK, maybe we need a quality inspection area and so on.
Meschede Jan SCW SCNDR: So we need to go all or through all processes and then try to build a kind of a structure there.
Meschede Jan SCW SCNDR: But therefore it's maybe easy to don't, UM, easier to don't look at the surely concept here, because we also have this in template, so we can use this as a kind of uh that, uh, a kind of a basis.
Meschede Jan SCW SCNDR: So we we can look through through the whole document and say and at the end with uh yes or a no like we need it or we we we don't need it and so on.
Meschede Jan SCW SCNDR: So this is so this is quite nice that we have a document which shows everything we got in the template now.
Meschede Jan SCW SCNDR: So maybe for some plans we need some additional things or areas and so on, but at least we have a document which is filled with all we got at the moment in the template warehouse processes.
Meschede Jan SCW SCNDR: UH-25 to two one and here we have the warehouse structure, master.
Meschede Jan SCW SCNDR: Mm-hmm.
Meschede Jan SCW SCNDR: Slotting.
Meschede Jan SCW SCNDR: Ohh, I'd just still on 200 uh 20 soon.
Meschede Jan SCW SCNDR: My bad.
Meschede Jan SCW SCNDR: So, but umm yeah.
Meschede Jan SCW SCNDR: As you can see here, it's, uh, it's uh already already updated.
Meschede Jan SCW SCNDR: So when someone do something here then it it always getting updated and also approved.
Meschede Jan SCW SCNDR: And when we look for for some things here, you can see, hey, what?
Meschede Jan SCW SCNDR: What?
Meschede Jan SCW SCNDR: Everything we got in the in the template right now and then we could just put some additional align here and say yes, no, no, no, yes and go through the whole structure and and have a look what we need or what we could need and at the end there we get a kind of a bigger picture out of it.
Meschede Jan SCW SCNDR: So when we yeah, as you can see here it is is a very very big document and you need to spend a lot of time with it.
Meschede Jan SCW SCNDR: But at least it is is very or it is quite nice because you can go through everything and then when we discuss something with the users, for example, or with some other DTA colleagues and so on and they say, hey, I don't know, did you heard about Hu type groups and you say let me quickly have a look, but OK Hu type groups, yes, we have these three here.
Meschede Jan SCW SCNDR: So handling units, single pack and euro pallets, that's it for the moment.
Meschede Jan SCW SCNDR: And so you can go through the whole thing now.
Meschede Jan SCW SCNDR: So you can, yeah, easy navigate through the whole thing, which is quite nice.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: Is this a somehow answering your question?
Meschede Jan SCW SCNDR: Sorry for this the long talking.
Orth Christian EXT Westhouse: Yeah, I mean, there is a kind of a default structure, let's say, and then this needs to be adapted for the planned I I think so this, yeah, I I also look by myself.
Orth Christian EXT Westhouse: So but yeah, thanks for this.
Meschede Jan SCW SCNDR: You're welcome.
Meschede Jan SCW SCNDR: I can should I share the link in the chat or?
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: It's just, uh, under the process requirements in the on template build SharePoint.
Meschede Jan SCW SCNDR: OK, very good.
Meschede Jan SCW SCNDR: Very good.
Meschede Jan SCW SCNDR: Then I would say we jump higher.
Araujo Thiago GCR ENGCL: Just second if if you can share the link today ZF template folders I would I would ask my manager but I'm new.
Araujo Thiago GCR ENGCL: I do not have it.
Araujo Thiago GCR ENGCL: If you can share it with.
Meschede Jan SCW SCNDR: Yes, sure, sure.
Meschede Jan SCW SCNDR: And uh, for the for the SharePoint SharePoint I also shared on the last appointment some some links but I think you did not participate or could not participate.
Meschede Jan SCW SCNDR: I I think it was just a just shared with you the meeting.
Meschede Jan SCW SCNDR: So this is the link to the SharePoint and yeah maybe in some later sessions we can.
Meschede Jan SCW SCNDR: We also will spend some time in this, but we did last last week.
Meschede Jan SCW SCNDR: So I yeah, yeah.
Meschede Jan SCW SCNDR: We also can do that or when you have some questions I can I can help you for sure.
Meschede Jan SCW SCNDR: Mm-hmm.
Meschede Jan SCW SCNDR: Yes, maybe you also need to request the the the access to it, but just have a look so everything should be clarified at the end.
Meschede Jan SCW SCNDR: OK, UM, good.
Meschede Jan SCW SCNDR: Let's go back to the system.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: So yeah, we did talk about the the product master data.
Meschede Jan SCW SCNDR: So we can define define a few strategies there, which is very helpful and sometimes also needed.
Meschede Jan SCW SCNDR: For example, the putaway.
Meschede Jan SCW SCNDR: Umm yeah, product description.
Meschede Jan SCW SCNDR: I don't think that I need to say something for that here.
Meschede Jan SCW SCNDR: Depends handling unit?
Meschede Jan SCW SCNDR: Umm.
Meschede Jan SCW SCNDR: I guess most of you should also be familiar with, so handling unit is is needed for for EWM so you are handling unit is always the packaging material combined with the product or a component.
Meschede Jan SCW SCNDR: And this together builds up or builds a handling unit which we need for moving material inside the EWM warehouse.
Meschede Jan SCW SCNDR: Umm yeah, the status is also also very very nice.
Meschede Jan SCW SCNDR: P2, for example, means a free for production, which is the case because this materials lay on the production storage bin here.
Meschede Jan SCW SCNDR: So PSA means production supply area and let's be in also click on this here the storage types.
Meschede Jan SCW SCNDR: So there's also with this forehead.
Meschede Jan SCW SCNDR: Uh for help.
Meschede Jan SCW SCNDR: Very, very needful here.
Meschede Jan SCW SCNDR: Just so you can see that this is also production supply milk run for example, and so this status is changed that it's unrestricted use for production.
Meschede Jan SCW SCNDR: Umm.
Meschede Jan SCW SCNDR: OK, so the batch number we already, yeah, shortly talked about it.
Meschede Jan SCW SCNDR: And as this is a a test system for the integration test, somebody said just NPR new.
Meschede Jan SCW SCNDR: So use this as a batch number and for every material the same.
Meschede Jan SCW SCNDR: So then you don't have kind of struggles when you when you.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: And then here is just described again the the warehouse number and the the storage bin and so on and the quantity and some stuff again and which is also very interesting.
Meschede Jan SCW SCNDR: You still have some connection with the goods receipt.
Meschede Jan SCW SCNDR: As you can see, so you still see goods receipt date and time here, so it's very nice that you can in combination with for example a Jew or product number or batch number and so on and search for the for the incoming goods.
Meschede Jan SCW SCNDR: For example, yeah or or or the how incoming goods that was that was garbage.
Meschede Jan SCW SCNDR: I mean for the connected delivery, we booked the goods in that way.
Meschede Jan SCW SCNDR: OK umm so for example this year we are at available stock now.
Meschede Jan SCW SCNDR: So this looks like a kind of a overview only, but we can also mark every line or mark every line and when we for example CE these fields above here, then we can open the second screen.
Meschede Jan SCW SCNDR: Here, it's always this three screens, so the folder structure, the first line and then the next stage is when you kind of go into in the more detail and and for example when we mark this line, we also can create for example a warehouse task warehouse task is just a single material movement.
Meschede Jan SCW SCNDR: So when we look at this year, so we see that this is this is already in production.
Meschede Jan SCW SCNDR: I I mean maybe it belongs there. I'm not sure, but maybe we leave it.
Meschede Jan SCW SCNDR: Maybe it's needed for our test, but here for example, the goods receipt zone we see.
Meschede Jan SCW SCNDR: Hey, it's also has the status, I mean the status is maybe wrong, but because it's not in a production zone but have this status, the unrestricted use for production.
Meschede Jan SCW SCNDR: So at the end, I guess we can book it there.
Meschede Jan SCW SCNDR: So when you click on on on warehouse task here and then a new window opens and for example here you can choose a different uh.
Meschede Jan SCW SCNDR: A different quantity.
Meschede Jan SCW SCNDR: So you can change, you can just move one or whatever, but we let it at 5:00.
Meschede Jan SCW SCNDR: At the moment, warehouse process type is the kind of movement we want to do.
Meschede Jan SCW SCNDR: So for example, put away and so on.
Meschede Jan SCW SCNDR: But this 999, for example, means warehouse supervision.
Meschede Jan SCW SCNDR: It is a kind of yeah, including all.
Meschede Jan SCW SCNDR: So it's just and when the yeah, I don't know how to say it.
Meschede Jan SCW SCNDR: It's a kind of a master movement, so it includes all so we don't don't need to to have a look at the moment.
Meschede Jan SCW SCNDR: Do we want to return scrap?
Meschede Jan SCW SCNDR: Put away something so for testing testing cases, we always take the 999 or I always take the 999 next is destination.
Meschede Jan SCW SCNDR: We can look.
Meschede Jan SCW SCNDR: Hey, what is this field?
Meschede Jan SCW SCNDR: So we click on it for help.
Meschede Jan SCW SCNDR: Then we see, hey, this is the this is the type again.
Meschede Jan SCW SCNDR: So when we have a look here, P001 there this component belongs, then we can double click on it.
Meschede Jan SCW SCNDR: This is always the plant number.
Meschede Jan SCW SCNDR: It should be 0001.
Meschede Jan SCW SCNDR: Yes, the section warehouse number and this last field should then be the storage bin.
Meschede Jan SCW SCNDR: So when we click on it, we see, hey, there's just one bin available.
Meschede Jan SCW SCNDR: This is also the thing I I said that the at the short explanation regarding Christian's question at least we should have one storage bin in a storage type where we can book something on.
Meschede Jan SCW SCNDR: It's not like in M where you book directly on storage types, and it's without any storage bins.
Meschede Jan SCW SCNDR: So here we can see, hey, it is not empty.
Meschede Jan SCW SCNDR: I mean, we see it also here, that there are minimum lays this this 10 pieces, but nevertheless it has the same batch number.
Meschede Jan SCW SCNDR: So I think we don't will get a problem here.
Meschede Jan SCW SCNDR: You can double click it and then you could say hey, we want to directly confirm the warehouse task or we say we just open or create a warehouse task and want to wait with the confirmation till we picked the material.
Meschede Jan SCW SCNDR: So I would say we do not confirm it yet.
Meschede Jan SCW SCNDR: Click on create, then a message shows up.
Meschede Jan SCW SCNDR: Display log OK Warehouse task 2 to one created warehouse Order 182 created so and yeah I would say we copy it.
Meschede Jan SCW SCNDR: And.
Meschede Jan SCW SCNDR: Close it.
Meschede Jan SCW SCNDR: So maybe now we have the problem.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: As you can see here, our material is gone because it's kind of on the way because it's and not any mayor, not not anymore on available stock.
Meschede Jan SCW SCNDR: We cannot use this material at the moment because we it's already included in a warehouse task and so we did talk now, OK, how can I remember now my warehouse task, how can I confirm it and so on.
Meschede Jan SCW SCNDR: So therefore we have a have our folder structure.
Meschede Jan SCW SCNDR: Again, warehouse task is a document and when we have here this warehouse order or warehouse tasks we can have a we can have a look and see a lot of things we can search for especially on directly the warehouse number but we could also look for creation dates so I can just put today and say OK and then I see oh that's a lot of then I go back there and say hey just the open ones and then I see OK at the moment I'm just dis warehouse tasks are open at the moment but we did.
Meschede Jan SCW SCNDR: Copy it.
Meschede Jan SCW SCNDR: Therefore, maybe I'm not sure if you already did talk about, but when you look on your keyboard and press Windows V so Windows V, then this kind of thing opens here.
Meschede Jan SCW SCNDR: It's a list with everything you copy it during the whole day, so this deletes after the day or after 25 hours.
Meschede Jan SCW SCNDR: I'm I'm not sure about, but when you press Windows V activate it, everything you copy with control C will be saved temporarily and this list, so it is very, very helpful when we look at my list here, there's some some things I copied so the warehouse order and also the warehouse task and I cannot forget it now.
Meschede Jan SCW SCNDR: So that's very nice.
Meschede Jan SCW SCNDR: When we delete it so also there is some some things maybe you say let's go back to to for example available stock.
Meschede Jan SCW SCNDR: So we we have a kind of a more complex the a process for example we can say hey, we need the storage bin, the product, the quantity and also the batch.
Meschede Jan SCW SCNDR: I copied all of that.
Meschede Jan SCW SCNDR: Then we go into our process.
Meschede Jan SCW SCNDR: For example, here we can close this again.
Meschede Jan SCW SCNDR: Go on the warehouse order and then I just press Windows V for example and have everything here I copied so storage bin, product number, quantity, batch and it's saved here.
Meschede Jan SCW SCNDR: That's very nice to work in the system and it saves you a lot of time searching for some things and sometimes you search for some information, then go to the next transaction.
Meschede Jan SCW SCNDR: So this is gone and then you are at the next transaction and there is, hey, what was the storage bin and you need to think about it.
Meschede Jan SCW SCNDR: So this saves you.
Meschede Jan SCW SCNDR: And hopefully a lot of time, but back to the warehouse order warehouse order is always the, I would say the corpus or the frame wherever host tasks are inside or whatever warehouse tasks get created out of.
Meschede Jan SCW SCNDR: So one warehouse order could have more warehouse tasks, but one warehouse task can always have one warehouse order.
Meschede Jan SCW SCNDR: So the warehouse task is the smallest working package I would say in the in the system.
Meschede Jan SCW SCNDR: OK, so we searching for our warehouse order Windows V and we say, hey, warehouse order, OK, want to 182 execute and then we see our order here is the the process type we choose.
Meschede Jan SCW SCNDR: So the warehouse supervision or what it was then for example my user here the creation date and the creation time and the warehouse order itself.
Meschede Jan SCW SCNDR: So yeah, you can also as you can see you can click on it, then a more detailed window opens and you can also see here for example the warehouse task which is in this warehouse order now.
Meschede Jan SCW SCNDR: As you can see here, the two to one it's here shown and you could could do something here.
Meschede Jan SCW SCNDR: Now and and and yeah, work with this with this without task.
Meschede Jan SCW SCNDR: But I would say this is not really needed because when you mark the line, you always have your, your, your buttons above here.
Meschede Jan SCW SCNDR: So warehouse tasks, for example, and then you like the gets also directly shown the warehouse task.
Meschede Jan SCW SCNDR: So this warehouse task is open at the moment.
Meschede Jan SCW SCNDR: Let me quickly open another screen.
Meschede Jan SCW SCNDR: Oops.
Meschede Jan SCW SCNDR: No.
Meschede Jan SCW SCNDR: Let's CW 10. Hey.
Meschede Jan SCW SCNDR: Ooh, I'm getting tired for today.
Meschede Jan SCW SCNDR: So stock and bin, stock and bin we have here this umm, available view uh, this should be our material.
Meschede Jan SCW SCNDR: So here we have our components and as you remember before there was, there was kind of a separate line here or not kind of a there was a fourth line here with the goods receipt area.
Meschede Jan SCW SCNDR: So when we switch back to our other screen, then we see hey, this warehouse task has no status yet, so it is still open.
Meschede Jan SCW SCNDR: But when we mark the line and click on our buttons here you can see you can have a lot of informations here or a lot of actions.
Meschede Jan SCW SCNDR: Here we could directly for example confirm the warehouse task now and they are also different ways you can go into warehouse order and then into the warehouse task and create a yeah confirm there you can confirm it here in the background you could use uh mobisys for example.
Meschede Jan SCW SCNDR: So there are different ways.
Meschede Jan SCW SCNDR: But uh, let's confirm the warehouse task.
Meschede Jan SCW SCNDR: And when I when I continue here, then the status should be changing.
Meschede Jan SCW SCNDR: So as you can see, see which means confirmed.
Meschede Jan SCW SCNDR: Warehouse status is confirmed, so when we switch back to our other Overview, it is still the same.
Meschede Jan SCW SCNDR: It is still searched for material, so when we refresh it from we have our material back again here and not on goods receipt zone anymore.
Meschede Jan SCW SCNDR: So it's it's directly on on PSA or production supply area in the production area, OK, which is also maybe maybe helpful and I mean we don't have that much time left, but I would say this is a kind of a umm yeah ****** here we because we are using a lot of UM warehousing, space because we have the same material with the same batch on different handling units on the same storage bin.
Meschede Jan SCW SCNDR: So for example, you have a storage bin and you have 3 boxes there with the same material and the same same and batch.
Meschede Jan SCW SCNDR: So you could easily put the material out of two boxes into the third box and have everything in one box.
Meschede Jan SCW SCNDR: I would say we we we can do that to save some space here for example, just everything just examples here.
Meschede Jan SCW SCNDR: But I would say we do that, but for a second because I was, uh, talking again.
Meschede Jan SCW SCNDR: Uh, for the last 20 minutes, are there any questions so far?
Meschede Jan SCW SCNDR: Ah, Yan Okal.
Meschede Jan SCW SCNDR: Sorry, I I always I do not see it in presenting mode.
Meschede Jan SCW SCNDR: When somebody raised the hand.
Meschede Jan SCW SCNDR: So sorry for that.
Okal Jan GNL SCNDR1: So yeah, sorry, just one question.
Okal Jan GNL SCNDR1: If you do the partial move ohh for the partial, where if you move from 5 only three pieces with the 9999 move it does not create Hu for whatever you moved out.
Okal Jan GNL SCNDR1: Is there a way to during that move create at U?
Meschede Jan SCW SCNDR: Uh, yes, for sure we can.
Meschede Jan SCW SCNDR: Umm, we can directly test that.
Meschede Jan SCW SCNDR: So if I understand you right, can you see my screen again?
Meschede Jan SCW SCNDR: Yes. OK.
Meschede Jan SCW SCNDR: So let's mark a line here.
Meschede Jan SCW SCNDR: Create a warehouse task for example.
Meschede Jan SCW SCNDR: Just take two warehouse process Type 999, destination bin.
Meschede Jan SCW SCNDR: 01 and the A uh directly confirm it.
Meschede Jan SCW SCNDR: Warehouse task costs for confirmed, so you described it right?
Meschede Jan SCW SCNDR: So we booked out, we booked out the material out of the handling unit, but we have no handling unit for the for the new two pieces here.
Meschede Jan SCW SCNDR: So that is correct.
Meschede Jan SCW SCNDR: When you now want to have a now want to have a handling unit, we need to go to the the packaging area.
Meschede Jan SCW SCNDR: So to the store, the bin area for example, when we click on it.
Meschede Jan SCW SCNDR: And sorry that it's wrong, but we could go with this way here.
Meschede Jan SCW SCNDR: Yes, I want to continue.
Meschede Jan SCW SCNDR: Whatever was the question. UM.
Meschede Jan SCW SCNDR: Uh, give me a second. When you.
Meschede Jan SCW SCNDR: Yeah, you.
Meschede Jan SCW SCNDR: I mean you could could click on the on the Hu and you see this overview, but I'm at least we need to go to to the yeah, it's it's EWM pack. Yeah.
Goebel Christian SCW SCNDR3: And is there a difference between doing that step in a production supply area and then let's say normal storage bin?
Meschede Jan SCW SCNDR: Them.
Meschede Jan SCW SCNDR: Sorry I did not I I don't know what you mean.
Meschede Jan SCW SCNDR: So when we have a.
Goebel Christian SCW SCNDR3: You're showing now the the warehouse type of the PSA production supply area.
Meschede Jan SCW SCNDR: Mm-hmm. Yes.
Goebel Christian SCW SCNDR3: And is there any difference from that to, let's say normal storage bin?
Meschede Jan SCW SCNDR: No, not not really.
Meschede Jan SCW SCNDR: What is the different is or?
Meschede Jan SCW SCNDR: What is the difference is when when we consume parts so we want to take them then we don't need the Hu anymore.
Meschede Jan SCW SCNDR: So for example, when you when you book with the ADHU some materials to production area for example, then with the ADGI you book it also out of the handling unit.
Goebel Christian SCW SCNDR3: And that was the that that's the question.
Goebel Christian SCW SCNDR3: If we do that, maybe in FG01, I think I'm not.
Goebel Christian SCW SCNDR3: I'm not 100% sure there.
Goebel Christian SCW SCNDR3: I think a new handling unit is automatically created or.
Meschede Jan SCW SCNDR: Umm, I'm not sure we can have a look, but I would.
Meschede Jan SCW SCNDR: Yeah, we can for sure.
Meschede Jan SCW SCNDR: That's a.
Meschede Jan SCW SCNDR: That's a good point, but I would finish that one here quick.
Meschede Jan SCW SCNDR: So for for Yang's question, we need to go now.
Meschede Jan SCW SCNDR: Who?
Meschede Jan SCW SCNDR: The SCM puck, so we have this Overview here work center.
Meschede Jan SCW SCNDR: We are always using like the supervision, a kind of a general working space.
Meschede Jan SCW SCNDR: So storage bin was the PSA minus 001.
Meschede Jan SCW SCNDR: So and when we open this, we see not anymore defined just with our material.
Meschede Jan SCW SCNDR: We did search for.
Meschede Jan SCW SCNDR: We see everything we material which lays on the PSA 001 and also our two pieces here without UM handling unit.
Meschede Jan SCW SCNDR: So here you see your different different things you can choose.
Meschede Jan SCW SCNDR: So for our case here, this is just right.
Meschede Jan SCW SCNDR: So we want to create an Hu.
Meschede Jan SCW SCNDR: So when you look here, you have our tools.
Meschede Jan SCW SCNDR: This one.
Meschede Jan SCW SCNDR: OK, so this is the Hu number product description, product number and this is the packaging material.
Meschede Jan SCW SCNDR: So we can double click on it.
Meschede Jan SCW SCNDR: Go on detail one and yeah, just copy it.
Meschede Jan SCW SCNDR: So when we look at here at creating an Adu, we copy it, storage, bin we wanted on PSA minus 001.
Meschede Jan SCW SCNDR: That's it.
Meschede Jan SCW SCNDR: So when we execute it, there should be a new.
Meschede Jan SCW SCNDR: It's you created so as you can see the Hu 8000102 is created and now the easiest part follows.
Meschede Jan SCW SCNDR: We take our material.
Meschede Jan SCW SCNDR: So this one here had Rick and drop put it in our Edu.
Meschede Jan SCW SCNDR: Then safe.
Meschede Jan SCW SCNDR: That's it.
Meschede Jan SCW SCNDR: So when we switch back to our other screen screen screen, umm, then we need to refresh.
Meschede Jan SCW SCNDR: Yes.
Meschede Jan SCW SCNDR: And as you can see here is our 102 Hu.
Meschede Jan SCW SCNDR: So we created just an Hu and put the material back in the Hu again.
Meschede Jan SCW SCNDR: So this is also very, very easy and project and drop.
Meschede Jan SCW SCNDR: I really like it, so when we look at our stuff here again, so we could also say, hey, we have this two things here now this one and also the three and I wanted back together.
Meschede Jan SCW SCNDR: It deletes him or it will delete the age you 102 again or or it will delete it and put it back to this again.
Meschede Jan SCW SCNDR: So when we save it.
Meschede Jan SCW SCNDR: Go back to our other screen.
Meschede Jan SCW SCNDR: Refresh and say yes we want continue.
Meschede Jan SCW SCNDR: Then one line is missing and it's not 5532.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: And yeah, that's that's that's it with this.
Meschede Jan SCW SCNDR: With this packing, I think it's it's it's very easy.
Meschede Jan SCW SCNDR: So you can also you can.
Meschede Jan SCW SCNDR: Do a lot of stuff here.
Meschede Jan SCW SCNDR: So you can also can repack the A A complete Hu or you could also repack a whole product for example.
Meschede Jan SCW SCNDR: So when you when we look at our our house here, then we have this 555 for example.
Meschede Jan SCW SCNDR: So we have this one here and we can say hey from this Hu, we don't want to take the whole quantity.
Meschede Jan SCW SCNDR: So we just want to take, for example, one piece.
Meschede Jan SCW SCNDR: So yeah, that's.
Meschede Jan SCW SCNDR: What is the detailed detailed 2 our our handling unit is there?
Meschede Jan SCW SCNDR: For example, we want to take one piece here, so we have the source age you, the next is the material.
Meschede Jan SCW SCNDR: This is the product.
Meschede Jan SCW SCNDR: This is the material quantity we say one and we say oops, not minus, we say peace and then the destination bin is for example the 999.
Meschede Jan SCW SCNDR: So we take the 999.
Meschede Jan SCW SCNDR: Then the packaging material, we also saved it.
Meschede Jan SCW SCNDR: You see, this is very helpful.
Meschede Jan SCW SCNDR: And there's storage.
Meschede Jan SCW SCNDR: Build is the PSA I know 001 and then when we execute you see one piece switched or were moved to the next one.
Meschede Jan SCW SCNDR: So when I save it, it is done.
Meschede Jan SCW SCNDR: Umm yeah, let's close this and then to refresh because we are unfortunately running out of time.
Meschede Jan SCW SCNDR: As you can see, the changes as well changes were also done here.
Meschede Jan SCW SCNDR: But what is important?
Meschede Jan SCW SCNDR: This looks like very easy and very simple and you can do what you want and trying to play here or kind of play here.
Meschede Jan SCW SCNDR: But when we look at the for example, the documents again and look for the warehouse tasks and we say, hey, OK, we want to see everything, especially the confirmed ones and we don't search for a special warehouse task.
Meschede Jan SCW SCNDR: We search for a product, so let's take our product and also maybe just set a filter for creation time and.
Meschede Jan SCW SCNDR: Justice.
Meschede Jan SCW SCNDR: Execute then you see there is a lot of things going on and when we go to a created apps put it like this.
Meschede Jan SCW SCNDR: You can see I mean this is Shirley time.
Meschede Jan SCW SCNDR: So or UK time.
Meschede Jan SCW SCNDR: So it's one hour behind, which is kind of misleading.
Meschede Jan SCW SCNDR: Bad example from my side here, but this should be 1457 and but you can see here all of the movements.
Meschede Jan SCW SCNDR: So we just switched some materials between the Hu, but we never lost the traceability.
Meschede Jan SCW SCNDR: So when you see these things here.
Meschede Jan SCW SCNDR: This is process type 3040, which means transfer.
Meschede Jan SCW SCNDR: Yeah.
Meschede Jan SCW SCNDR: Repack.
Meschede Jan SCW SCNDR: So everything we did is saved within a warehouse task.
Meschede Jan SCW SCNDR: So we always have the the traceability where or when we did something and when we moved some material and so on.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: UM, we have.
Meschede Jan SCW SCNDR: Uh, just one minute left, which is not that nice but.
Meschede Jan SCW SCNDR: And I would say for the questions I I did, yeah, I did.
Meschede Jan SCW SCNDR: I'm.
Meschede Jan SCW SCNDR: I'm I'm during the day and and yesterday and I thought what questions could be there and for me it would be very nice to.
Meschede Jan SCW SCNDR: Have kind of a.
Meschede Jan SCW SCNDR: Yeah, we should for next meeting.
Meschede Jan SCW SCNDR: Maybe you got open topics and integration test and maybe you need some some help or some informations regarding preparing the UAT so the user acceptance test what you can see here.
Meschede Jan SCW SCNDR: What I already did right is some topics that came out of the training session with the Asia Pacific, so they already, yeah, did have some things they want to talk about the next time and so please feel free if you have some some questions.
Meschede Jan SCW SCNDR: UM to yeah, hand them over to me and we can maybe discuss about different topics or explain something or take care about some current issues.
Meschede Jan SCW SCNDR: They have during your your talks and I would say FG01 try to create where else task umm with with.
Meschede Jan SCW SCNDR: Partial 1-2 tea.
Meschede Jan SCW SCNDR: This was from from Christian which was a good point.
Meschede Jan SCW SCNDR: Which is it?
Meschede Jan SCW SCNDR: 12 so are there any any questions at the moment or every any any things you need help with any kind of pickets or so on?
Ochoa Laura MNR UELP: I I do have a question, but this is related to the step where you created the handling unit for the two pieces.
Ochoa Laura MNR UELP: So my question is, is this something that in real life the the key user will do manually or this will be for example with mobisys or more automatic?
Meschede Jan SCW SCNDR: Yeah, you can.
Ochoa Laura MNR UELP: Umm.
Meschede Jan SCW SCNDR: You scan the Hu, use everything I typed in all the things.
Meschede Jan SCW SCNDR: You can also scan so for example the Hu number and then you can also repack it.
Meschede Jan SCW SCNDR: So it is.
Meschede Jan SCW SCNDR: There is also the case that we don't use.
Meschede Jan SCW SCNDR: We do not use the movement 9999 so and also do not directly confirm it.
Meschede Jan SCW SCNDR: So because there is some physical work behind.
Meschede Jan SCW SCNDR: So I mean we can book it within seconds.
Meschede Jan SCW SCNDR: It doesn't matter if the subway or with the hand scanner, but there's some physical task behind.
Meschede Jan SCW SCNDR: So yeah, the all the tasks should always.
Meschede Jan SCW SCNDR: At this time, be confirmed when the work is done, yeah.
Meschede Jan SCW SCNDR: Yeah.
Meschede Jan SCW SCNDR: You're welcome.
Meschede Jan SCW SCNDR: We can also have a look at the at one of the next sessions.
Meschede Jan SCW SCNDR: I'm, as you can see, one of the one of the things that came out of.
Meschede Jan SCW SCNDR: Out of Asia Pacific is a mobisys transaction overview, which short explanation because uh, when we look at the mobisys transactions, it's kind of the yeah kind of and and and and yeah, how can I say that not that easy to understand because you have a have a lot of things here and you need to yeah it's difficult to to navigate through it at the beginning but we can have a look at it. Yeah.
Meschede Jan SCW SCNDR: So do you have open topics regarding warehousing, at the moment?
Meschede Jan SCW SCNDR: So for example, here was a ticket.
Meschede Jan SCW SCNDR: I will will take care of or not take care of, but I I will look through it and maybe can help and maybe we can find a solution together or can clarify some things in the next session with Asia Pacific.
Meschede Jan SCW SCNDR: Who?
Meschede Jan SCW SCNDR: You have some some similar things at the moment.
Segura Jose Angel MNR SCNDR1: Yes, well for for my side, yes, Jan, there are some some questions and and some tickets but but maybe I can I can send you by by email and maybe we can review it in in the next session, right?
Meschede Jan SCW SCNDR: Umm yeah, sure, sure.
Meschede Jan SCW SCNDR: I will also do it with this things here.
Meschede Jan SCW SCNDR: So, and there's also the was also the question raised up show organizational structure of template.
Meschede Jan SCW SCNDR: I also received that per mail and put it in this file here afterwards, so therefore we also can take for example five 10-15 minutes depends on the tasks before we jump into a process for example, or start with a general training.
Meschede Jan SCW SCNDR: So therefore, it's this kind of a placeholder.
Meschede Jan SCW SCNDR: So sometimes maybe it's more more needful for you to talk about a current issue we have in warehousing, area instead of, yeah, doing or explain how to scrap the material or how to do a stock transfer order or whatever we're automatic guided put away.
Meschede Jan SCW SCNDR: So it depends on the needs of the group.
Meschede Jan SCW SCNDR: So we want to have a kind of placeholder here for every kind of problem.
Meschede Jan SCW SCNDR: So if there if something comes up to your mind, please feel free to write in the chat.
Meschede Jan SCW SCNDR: Write me directly in the chat or write an email, umm.
Meschede Jan SCW SCNDR: If there's, or if it are some some, some small questions or something like that, you can also put it in this in this group chat here.
Meschede Jan SCW SCNDR: So as you know, this group chat here.
Meschede Jan SCW SCNDR: I marked it for me here with and and Jia Pacific and also UM and Amir America training.
Meschede Jan SCW SCNDR: So do you can always.
Meschede Jan SCW SCNDR: You can always use this and yeah I'm ask some questions and maybe when I do not answer directly some of the colleagues will because he also know something and.
Segura Jose Angel MNR SCNDR1: OK, great.
Meschede Jan SCW SCNDR: OK.
Meschede Jan SCW SCNDR: Are there more questions?
Giron Jorge MNR SCNDR1: Yeah, I have a this more general question.
Giron Jorge MNR SCNDR1: I tried to enter the link that you shared, however I couldn't enter to the supply chain or purchasing links.
Meschede Jan SCW SCNDR: Umm.
Giron Jorge MNR SCNDR1: No.
Giron Jorge MNR SCNDR1: It just says that I don't.
Giron Jorge MNR SCNDR1: I don't have access and I could.
Giron Jorge MNR SCNDR1: It just says access denied but I cannot request for access.
Meschede Jan SCW SCNDR: Yeah, please do.
Giron Jorge MNR SCNDR1: Thank you very much.
Giron Jorge MNR SCNDR1: Appreciate it.
Meschede Jan SCW SCNDR: You're welcome.
Meschede Jan SCW SCNDR: OK, good.
Meschede Jan SCW SCNDR: And we are 7 minutes overtime.
Meschede Jan SCW SCNDR: Sorry for that.
Meschede Jan SCW SCNDR: It was a bad bad the I don't know the done from my side that we are that we did not get through all the things.
Meschede Jan SCW SCNDR: We also missed the point in theory, but we can take care of that in the next session quickly, so everything good, OK.
Meschede Jan SCW SCNDR: And if there are no more questions, I would say I want to say thank you for participating and then we see us next week same time.
Segura Jose Angel MNR SCNDR1: OK, great.
Meschede Jan SCW SCNDR: Thank you guys.
