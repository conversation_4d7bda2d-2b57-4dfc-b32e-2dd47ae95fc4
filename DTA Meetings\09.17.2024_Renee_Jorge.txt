<PERSON> Susan LVN SCNA12: We can do the cardboard boxes is what you're saying, <PERSON>, that we buy or whatever other peripherals, yeah.
<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON> MRS SCNA12: Yeah, there was a list of vendors that we can confirm, but everything else I can't.
<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> MRS SCNA12: I don't know.
<PERSON><PERSON> MNR SCNDR1: OK.
<PERSON><PERSON> Renee MNR SCNDR1: So so maybe I can hear.
<PERSON><PERSON> Renee MNR SCNDR1: Maybe I can get all the questions or the missing parts with with you and maybe we can have another session.
<PERSON><PERSON> Renee MNR SCNDR1: I don't know. What do you think?
M<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> MRS SCNA12: Because I'm looking at this, I'm looking at your material numbers and none of those material numbers look familiar to anyone that I use.
M<PERSON><PERSON><PERSON><PERSON> Mary-<PERSON> MRS SCNA12: So that's why I'm a little bit worried.
M<PERSON><PERSON><PERSON><PERSON> Mary-<PERSON> MRS SCNA12: None of those are my material numbers.
<PERSON> Susan LVN SCNA12: We have a description of what these things are.
Giron Jorge MNR SCNDR1: I was going to ask <PERSON> if she can put the description just beside the material number.
<PERSON> Susan LVN SCNA12: Yeah, I think that would be helpful.
<PERSON> Susan LVN SCNA12: So we know what we're talking.
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> MRS SCNA12: And I can tell by just the part number, the material number that it's not ours.
<PERSON> Susan LVN SCNA12: That 16 M looks similar to a cardboard box, but other than that, that's.
<PERSON> <PERSON> LVN SCNA12: My lame guess here.
Westrup <PERSON> MNR SCNDR1: <PERSON> give me a second because he's here.
Westrup <PERSON> MNR SCNDR1: Everywhere.
McNeely <PERSON>-<PERSON> MRS SCNA12: I think I just seen it. I think I just seen it in.
<PERSON>n Jorge MNR SCNDR1: Yeah, if you if you go back and then just can you hide it again, not just undo and it's under your far right, it's under your far right.
Giron Jorge MNR SCNDR1: Yeah, there you go.
McNeely Mary-Kathryn MRS SCNA12: Yep, there it was under in the Orange.
McNeely Mary-Kathryn MRS SCNA12: A little bit over to your right, yeah.
Westrup Renee MNR SCNDR1: Oh, OK.
McNeely Mary-Kathryn MRS SCNA12: Yeah.
Westrup Renee MNR SCNDR1: Got it.
Giron Jorge MNR SCNDR1: There you go.
Westrup Renee MNR SCNDR1: Let me start with this.
Giron Jorge MNR SCNDR1: Not we have it here. Just. There you go. All material number is going to be the same. I have seen that the materials are not changing most of the times they are.
Giron Jorge MNR SCNDR1: Yeah, they are the same.
McNeely Mary-Kathryn MRS SCNA12: To see all of those material numbers there, I don't buy any of those.
McNeely Mary-Kathryn MRS SCNA12: All those old column le, none of those. Do I buy?
Westrup Renee MNR SCNDR1: OK. But you are telling that box, yes.
McNeely Mary-Kathryn MRS SCNA12: Well, this thing is that's like a box.
McNeely Mary-Kathryn MRS SCNA12: It's part of the customer packaging that we don't buy.
Wilson Susan LVN SCNA12: Is it come from the customer itself?
Wilson Susan LVN SCNA12: OK.
Wilson Susan LVN SCNA12: So there's inbound and outbound. We need to look at the outbound.
McNeely Mary-Kathryn MRS SCNA12: Well, I think they made outbound and inbound part of this npm.
McNeely Mary-Kathryn MRS SCNA12: I think if I'm right and then like I said, I gave them my specific vendor ID and said any number associated with this vendor ID would be.
McNeely Mary-Kathryn MRS SCNA12: An outbound packaging that we purchase.
McNeely Mary-Kathryn MRS SCNA12: If you sort column LE by like 16 M or 14 or yeah like that 13 M might be.
McNeely Mary-Kathryn MRS SCNA12: Yeah, there you go.
McNeely Mary-Kathryn MRS SCNA12: So those are like ones that we buy, yes.
Westrup Renee MNR SCNDR1: OK.
Westrup Renee MNR SCNDR1: So there are like, let me tell you 110 items sort of.
McNeely Mary-Kathryn MRS SCNA12: There's like 16 M.
McNeely Mary-Kathryn MRS SCNA12: There's it's usually 1617 M.
McNeely Mary-Kathryn MRS SCNA12: I'm not sure of the nomenclature, but I I think that's normally most of them.
McNeely Mary-Kathryn MRS SCNA12: But it's by I gave them a list of vendor IDs that we buy that are packaging vendors.
McNeely Mary-Kathryn MRS SCNA12: That's how I gave him the list, if that helps.
McNeely Mary-Kathryn MRS SCNA12: I don't know if I'm helping you or not.
Westrup Renee MNR SCNDR1: Did did it give it to you?
Westrup Renee MNR SCNDR1: Like did Dudley's.
Westrup Renee MNR SCNDR1: Do you have it, Jorge?
Giron Jorge MNR SCNDR1: Yes, I remember that America send a list of the packaging materials.
Giron Jorge MNR SCNDR1: However, I remember that we couldn't migrate all of them because we were waiting for the packaging instructions.
Giron Jorge MNR SCNDR1: That was my until I I get.
Giron Jorge MNR SCNDR1: I will need to, you know, search on my emails and and yeah.
Giron Jorge MNR SCNDR1: Have some context, but I think for these ones they are correctly classified.
Giron Jorge MNR SCNDR1: They are, as, let me just remember, the nomenclature. They are cqm.
Giron Jorge MNR SCNDR1: Four, if I remember. And they are one way packaging outbound.
Wilson Susan LVN SCNA12: Maybe we'll look at it under that Cqn 4 then only.
Wilson Susan LVN SCNA12: Well, I don't know. I don't know.
Giron Jorge MNR SCNDR1: Yeah, if you can. If you remove the filter from that 16 M and then just put the cqm 4, they should be all.
Giron Jorge MNR SCNDR1: The ones that all the packaging that we procured.
Giron Jorge MNR SCNDR1: Not not all right, but yeah, the ones that we put like a test, yeah.
McNeely Mary-Kathryn MRS SCNA12: Yeah, that 21 is a weird one that we do purchase also, yes, but.
McNeely Mary-Kathryn MRS SCNA12: That looks right.
Wilson Susan LVN SCNA12: Do you have the vendor associated with this in this list?
Westrup Renee MNR SCNDR1: No, I I will need to add it.
Giron Jorge MNR SCNDR1: I know.
Giron Jorge MNR SCNDR1: Well, yeah, we haven't reached that point yet.
Giron Jorge MNR SCNDR1: First, I think we they migrate the vendors and then the material master's but not you know the linkage with the purchasing documents.
Wilson Susan LVN SCNA12: OK.
Giron Jorge MNR SCNDR1: Yep.
Westrup Renee MNR SCNDR1: My only concern is I don't see seven M for example.
McNeely Mary-Kathryn MRS SCNA12: Well, if you don't have all of the vendors, if you don't have all the part numbers and you wouldn't have, you wouldn't have everything. So.
Giron Jorge MNR SCNDR1: I remember that we just put like how can I say?
Giron Jorge MNR SCNDR1: A small amount just to test.
Giron Jorge MNR SCNDR1: The process.
Giron Jorge MNR SCNDR1: Yeah, we we didn't get the full list.
McNeely Mary-Kathryn MRS SCNA12: So there's gonna be a lot more, yeah.
Giron Jorge MNR SCNDR1: Yeah, there there is going to be a lot.
Giron Jorge MNR SCNDR1: How many do we have, Renee?
Westrup Renee MNR SCNDR1: Yeah, I think it's a good number is 111.
Giron Jorge MNR SCNDR1: I think they are were like around 200, but I don't remember.
Giron Jorge MNR SCNDR1: I will need to check my emails like I mentioned.
Giron Jorge MNR SCNDR1: Bevan will.
Giron Jorge MNR SCNDR1: Yeah, correct.
Giron Jorge MNR SCNDR1: Something like that.
Wilson Susan LVN SCNA12: But this isn't bad for a sample I guess if that's what we're doing.
Giron Jorge MNR SCNDR1: Correct, correct.
Giron Jorge MNR SCNDR1: That's where we are doing with the TM1. We just have, you know.
Giron Jorge MNR SCNDR1: We are just creating the templates for us to. How can I say map the rest of the materials if I can call it that way?
McNeely Mary-Kathryn MRS SCNA12: So what do we need to do on this end?
McNeely Mary-Kathryn MRS SCNA12: What do you need me to do?
Westrup Renee MNR SCNDR1: Just like if that makes sense to you to use this ones for the integration test and for the first migration.
Westrup Renee MNR SCNDR1: Correct me Jorge, if I'm wrong.
Giron Jorge MNR SCNDR1: Yeah, it's. Well, it's, it's it's, it's almost like the same with the Bender thing.
Giron Jorge MNR SCNDR1: For you guys, as you are the owners of the data, we just need to present this to see if it makes sense of whatever we provide to be migrated right in this first validation we just see or detect if data of the DMF team you know have some.
Giron Jorge MNR SCNDR1: Issues with some of the material.
Giron Jorge MNR SCNDR1: See if they didn't migrated. Why, but I think we are in a good shape with Marshall.
Giron Jorge MNR SCNDR1: The good thing here is that you can start familiarizing with the new S4 language, for example.
Giron Jorge MNR SCNDR1: That you can identify easily that cqm 4.
Giron Jorge MNR SCNDR1: Those are the.
Giron Jorge MNR SCNDR1: I forgot.
Giron Jorge MNR SCNDR1: Let me just open the. Yeah, one way packaging outbound for example.
Wilson Susan LVN SCNA12: OK.
Giron Jorge MNR SCNDR1: And.
Wilson Susan LVN SCNA12: What do you have for the material group?
Wilson Susan LVN SCNA12: You know how I like my material group is that packaging?
McNeely Mary-Kathryn MRS SCNA12: That is packaging.
Giron Jorge MNR SCNDR1: We can check that you know we have here on LD.
Giron Jorge MNR SCNDR1: We have the material group.
Giron Jorge MNR SCNDR1: Then we can check how many material group do we have here, Rene.
Wilson Susan LVN SCNA12: Should be the same.
Giron Jorge MNR SCNDR1: I wanted to check that.
Giron Jorge MNR SCNDR1: So we are good.
Giron Jorge MNR SCNDR1: If we can check this, if it is correctly mapped, that's a good.
Giron Jorge MNR SCNDR1: That's a good that's a good validation, you know.
McNeely Mary-Kathryn MRS SCNA12: I don't that those are all cardboard though, Susan.
McNeely Mary-Kathryn MRS SCNA12: That's the only thing is because 16 M can be other part numbers too.
Wilson Susan LVN SCNA12: OK.
Wilson Susan LVN SCNA12: So we'll need to look at the description and then make sure it's the.
Wilson Susan LVN SCNA12: Actually cardboard.
McNeely Mary-Kathryn MRS SCNA12: Well, that's where if they would do it by vendor, it'd be a lot easier for us.
Wilson Susan LVN SCNA12: Amen, sister. But.
Giron Jorge MNR SCNDR1: I don't.
Giron Jorge MNR SCNDR1: The materials in the independence.
Wilson Susan LVN SCNA12: But when we turn this in, we gave you the vendor code though right on our part list.
Giron Jorge MNR SCNDR1: Yeah, but whenever we are migrating the master data, we don't put that information.
Wilson Susan LVN SCNA12: And understood, but at least we come back into it is what I'm getting at to help.
McNeely Mary-Kathryn MRS SCNA12: But but the problem is as I assume that they did. You guys do only one vendor?
McNeely Mary-Kathryn MRS SCNA12: Did you do 'cause? There was like I don't like 10 vendors. So for me to validate I'd have to know what vendors did you put in there to know what was missing.
Giron Jorge MNR SCNDR1: Mm hmm mm hmm.
McNeely Mary-Kathryn MRS SCNA12: Unless I'm thinking of something incorrectly.
Wilson Susan LVN SCNA12: Take that list and then extract the vendor code that was with that part. If that list is still around.
Wilson Susan LVN SCNA12: And then you know what vendor it came from.
McNeely Mary-Kathryn MRS SCNA12: I mean, I can assume, but I know for example a lot of these look like cardboard, so I know that royal has more than 111 parts.
Wilson Susan LVN SCNA12: How about we copy paste that into Emmy 2 M and then run it and it'll tell you the vendors that way.
Giron Jorge MNR SCNDR1: Yeah, you can do that as well.
Westrup Renee MNR SCNDR1: In network.
Giron Jorge MNR SCNDR1: Oh, it's some pretty.
Giron Jorge MNR SCNDR1: Yeah, but you need to share.
Giron Jorge MNR SCNDR1: You need to share that list to Mary Kay and probably she can just run it, yeah.
Wilson Susan LVN SCNA12: And then we could from there, understand?
McNeely Mary-Kathryn MRS SCNA12: So I guess what I'm trying to figure out is what's the point of this if you guys picked and just decided to pick and choose which one you pulled out, how can I validate what you decided to pick out?
McNeely Mary-Kathryn MRS SCNA12: Did you pick one vendor or did you pick more than one vendor because it looks like you picked more than one vendor.
Wilson Susan LVN SCNA12: And that didn't mean too well.
Wilson Susan LVN SCNA12: Send it to me when you get it.
Wilson Susan LVN SCNA12: I don't know what they did to tell you the truth, but.
McNeely Mary-Kathryn MRS SCNA12: And that's what I'm saying.
McNeely Mary-Kathryn MRS SCNA12: It's hard to validate something and I don't know what they did.
Wilson Susan LVN SCNA12: So what are you trying to her point?
Wilson Susan LVN SCNA12: What do you want us to do?
Wilson Susan LVN SCNA12: Just validate the numbers that we know and say yeah, this is, but then all those other columns John Johns will have to.
Wilson Susan LVN SCNA12: Like the storage locations and if you got men's and Max's or I don't know what else you have in there, but we can say.
Wilson Susan LVN SCNA12: It's cardboard I guess, but we don't know all the other parameters.
McNeely Mary-Kathryn MRS SCNA12: That's the weird thing, because it's not gonna be John, John.
McNeely Mary-Kathryn MRS SCNA12: This is done through the material group material people.
McNeely Mary-Kathryn MRS SCNA12: So who is supposed to validate that portion of it?
McNeely Mary-Kathryn MRS SCNA12: The engineers, that's who's gonna interact going forward.
McNeely Mary-Kathryn MRS SCNA12: I mean, I I don't know.
McNeely Mary-Kathryn MRS SCNA12: Yeah, I.
Westrup Renee MNR SCNDR1: OK.
Wilson Susan LVN SCNA12: Chandler's on what says you.
Wilson Susan LVN SCNA12: A Wise 1 Wallace.
Wallace Chandler MRS UEAUP1: I thought we were just supposed to kind of validate that the the mappings were working properly.
Wallace Chandler MRS UEAUP1: So maybe it might help if we look at.
Wallace Chandler MRS UEAUP1: You know, a split screen of S4, HANA and PRD and then make sure that we're at least in the same ballpark with our values.
Wallace Chandler MRS UEAUP1: That's kind of how I approached some PM material and I think that works because right my my brain works in the PRD system.
Wallace Chandler MRS UEAUP1: Not quite the way S4 HANA works yet.
McNeely Mary-Kathryn MRS SCNA12: From Susan's point on the NPM side, all we care about is that we've got the vendor, we've got the material group and we can order it from how it's set up in the system.
McNeely Mary-Kathryn MRS SCNA12: I mean, I care.
McNeely Mary-Kathryn MRS SCNA12: Don't get me wrong, but you know what I'm saying?
Wilson Susan LVN SCNA12: Yeah, we don't say the storage location.
Wilson Susan LVN SCNA12: How many? Keep on hand what?
McNeely Mary-Kathryn MRS SCNA12: Yeah, that's what I worry about.
Wilson Susan LVN SCNA12: I mean, what are you want?
Wilson Susan LVN SCNA12: What do you want validated in these columns?
Wilson Susan LVN SCNA12: All of them or I. I don't understand. What? What do you want?
Westrup Renee MNR SCNDR1: This one.
Westrup Renee MNR SCNDR1: The storage location, the procurement type.
Westrup Renee MNR SCNDR1: MRT type MRP controller for chasing group.
Wilson Susan LVN SCNA12: That would be yes.
Wilson Susan LVN SCNA12: OK, so we can do the if you say, hey, I'll look at the yellow ones, we can do that.
Wilson Susan LVN SCNA12: In our domain, but anything to do with the material side then?
Wilson Susan LVN SCNA12: We'll look to the material side to tell you what it is, I guess.
Westrup Renee MNR SCNDR1: Yeah. And and.
Wilson Susan LVN SCNA12: Does that make sense? Mary Kaye.
McNeely Mary-Kathryn MRS SCNA12: Well, I still don't understand.
McNeely Mary-Kathryn MRS SCNA12: They're wanting us to validate that everything came over. I if I don't know what values they used to pull all this out of, I can't tell them if they were missing anything.
McNeely Mary-Kathryn MRS SCNA12: Like I can go in the system and like like, like Chandler said.
McNeely Mary-Kathryn MRS SCNA12: Look at PRD.
McNeely Mary-Kathryn MRS SCNA12: Look at the new system and see and see that it it.
McNeely Mary-Kathryn MRS SCNA12: You know my part.
McNeely Mary-Kathryn MRS SCNA12: Numbers there and you know that type of thing, but I don't know if there's values that are missing that didn't come over.
Wilson Susan LVN SCNA12: I don't.
Wilson Susan LVN SCNA12: We're asking what's missing. I think they're just what's present.
Giron Jorge MNR SCNDR1: Red if you want, we can just do this check with the demo.
Giron Jorge MNR SCNDR1: That's also my perception.
Giron Jorge MNR SCNDR1: We cannot do furthermore, like you said, Mary Kay.
Giron Jorge MNR SCNDR1: We don't have like the complete full scope.
Giron Jorge MNR SCNDR1: You know, we are just testing like the migration tool works.
Giron Jorge MNR SCNDR1: You know, based on what we, the established criteria on the demos.
Giron Jorge MNR SCNDR1: That. Yeah, that's my understanding.
Westrup Renee MNR SCNDR1: Yep.
Wilson Susan LVN SCNA12: So in in Line 3 is that nomenclature PRD and line 2 the nomenclature S for HANA.
Giron Jorge MNR SCNDR1: Everything what you are seeing here is an extract from S for HANA.
Wilson Susan LVN SCNA12: OK.
Wilson Susan LVN SCNA12: And the goal is what's listed below in those columns.
Wilson Susan LVN SCNA12: That's accurate to what we want it to look like in S4 HANA.
Wilson Susan LVN SCNA12: But if we don't know what those columns relate to in PRD, to Mr. Wallace's point, we it's hard to to validate that.
Wilson Susan LVN SCNA12: And then you got a bunch of things hidden. So.
Giron Jorge MNR SCNDR1: Well, we just hide the the things that are not important for us.
Giron Jorge MNR SCNDR1: Or they are blank and for example what we can tell from this list is that for example we have.
Giron Jorge MNR SCNDR1: A filter the packaging materials, right?
Giron Jorge MNR SCNDR1: We already know that tqm 4R40 outbound one way packaging outbound.
Giron Jorge MNR SCNDR1: So we have we can notice here that it is important for us to know that these are set up as reorder point. If you scroll a little a little bit up.
Giron Jorge MNR SCNDR1: Renee, we have the MRP type that all of these they must have.
Giron Jorge MNR SCNDR1: Reorder point if they don't have reorder point it it it.
Giron Jorge MNR SCNDR1: It is useless that we have the MRP type BB.
Giron Jorge MNR SCNDR1: You know, I think those are the important things to take take from this.
McNeely Mary-Kathryn MRS SCNA12: I don't know that like I I know my buyer code is purchasing group MRP types.
McNeely Mary-Kathryn MRS SCNA12: I don't know the different types that are available in S4 HANA. I don't know what that should be, right or what should be wrong.
McNeely Mary-Kathryn MRS SCNA12: Zqno 4.
McNeely Mary-Kathryn MRS SCNA12: I that you just said it was right.
McNeely Mary-Kathryn MRS SCNA12: I don't know that.
Giron Jorge MNR SCNDR1: Right. But you don't that.
Giron Jorge MNR SCNDR1: And that's the main intent of, you know, to for you to get familiarized with the new language of As for HANA.
Giron Jorge MNR SCNDR1: So whenever you run the reports, you can easily identify I I know that for example MRP type is is also out of my scope but nevertheless I need to know the impact of these specific field. You know in order to know how we can.
Giron Jorge MNR SCNDR1: How can I say make the use?
Giron Jorge MNR SCNDR1: Make make make the most use of the tool.
Wilson Susan LVN SCNA12: So on the MRP controller, does that mean it runs on?
Wilson Susan LVN SCNA12: Mrp run and it says abracadabra based on your men's and Mac's. Here comes what to order.
Wilson Susan LVN SCNA12: Is that what the? Yeah. OK.
Wilson Susan LVN SCNA12: So where's your men's and Max's?
Wilson Susan LVN SCNA12: 'Cause we will not know that, but we will need to get that from the materials group.
Wilson Susan LVN SCNA12: Where do you have that?
Giron Jorge MNR SCNDR1: Let's take a look at that.
Giron Jorge MNR SCNDR1: Yeah, it's going to be hidden or probably under.
Giron Jorge MNR SCNDR1: You can unhide everything and then just.
Wilson Susan LVN SCNA12: Yeah.
Giron Jorge MNR SCNDR1: Find it.
Giron Jorge MNR SCNDR1: Control it.
Giron Jorge MNR SCNDR1: You.
Giron Jorge MNR SCNDR1: We we have the re order point correct.
Wilson Susan LVN SCNA12: So when it gets to.
Wilson Susan LVN SCNA12: Looks like they got everything 0. They're gonna order one on that instance.
Westrup Renee MNR SCNDR1: Needs 1000 because this is sorry I put it in.
Wilson Susan LVN SCNA12: I wanted to th 1000.
Giron Jorge MNR SCNDR1: It should be one.
Wilson Susan LVN SCNA12: Well, then, how much will it order?
Wilson Susan LVN SCNA12: Where does that say that?
Giron Jorge MNR SCNDR1: Will be on the Max.
Wilson Susan LVN SCNA12: It's not set up.
Giron Jorge MNR SCNDR1: Should be on the Max.
Giron Jorge MNR SCNDR1: So here you know we have identified that we if we have a reorder point we need to establish also Max you know.
Giron Jorge MNR SCNDR1: And that data catch and you know that's a good thing of that of this data validation that we encountered these type of things.
Wilson Susan LVN SCNA12: Yeah. Now I don't know this for sure, but a long time ago we talked about getting it over.
Wilson Susan LVN SCNA12: And then at one time the materials group were going to get help in establishing that how much to order once it got down to their minimum lot or their pre-order point?
Wilson Susan LVN SCNA12: And it doesn't look like nobody's done that 'cause it looks like it's all 0.
Giron Jorge MNR SCNDR1: Mm hmm mm hmm correct.
Giron Jorge MNR SCNDR1: So that that should be our focus for team to be.
McNeely Mary-Kathryn MRS SCNA12: But it doesn't exist in today's system. That's the problem.
McNeely Mary-Kathryn MRS SCNA12: That's probably why I didn't get migrated over the min. Max does not exist today in PRD.
McNeely Mary-Kathryn MRS SCNA12: Oh yeah, you have to put something, but yeah, yeah.
Wilson Susan LVN SCNA12: Yeah, yeah, yeah.
Wilson Susan LVN SCNA12: So that that's a conversation with Angie and John Johns and and Paul Simmons, I guess in terms of how to what to order and when.
Giron Jorge MNR SCNDR1: Right. How much inventory do you want to have whenever that that eight trigger the procurement proposal?
Wilson Susan LVN SCNA12: Or they could put it on a scheduling agreement and do it the way they're doing now.
Wilson Susan LVN SCNA12: But I heard they wanted to get away from that last, I think, but.
Wilson Susan LVN SCNA12: But again, the only thing for purchasing we can validate is that, hey, this is the part that we used to order from this vendor and and do the info record information. All the rest of it.
Giron Jorge MNR SCNDR1: And I think that the most important thing, yeah, the most important thing for for us in 13 I think is the material group classification because at the end of the day, it's the way that we are going to report our expenses.
Wilson Susan LVN SCNA12: And that too, yes.
Giron Jorge MNR SCNDR1: So that that from from, from my perspective that will be the most important thing to check at this point and the purchasing groups, you know that everything is correctly set up for us to be able to buy the, the, the, the whatever we're buying, right.
Giron Jorge MNR SCNDR1: That's my my goal.
Giron Jorge MNR SCNDR1: From a sourcing perspective, yes.
Wilson Susan LVN SCNA12: Question on the the MRP that you can put.
Wilson Susan LVN SCNA12: A trigger in there if it's going to be driven by MRP or not, we still have.
Wilson Susan LVN SCNA12: You can still put VB and all of that, but there's something in there that you turn off so it doesn't go on to MRP is is that? And I think that may be the MRP controller, but but I don't know what XX0 means.
Wilson Susan LVN SCNA12: Do you guys know?
Wilson Susan LVN SCNA12: I'm just going back in my brain to other SAP systems I've worked with.
Segura Jose Angel MNR SCNDR1: Oh, the the MRP controller is is the is.
Segura Jose Angel MNR SCNDR1: It's a person or or a group of person, yes.
Simmons Paul MRS UEAUP1: That's the planner.
Simmons Paul MRS UEAUP1: This is the planner.
Wilson Susan LVN SCNA12: What? So what's the trigger in the inventory? Master, that says I want to run this on MRP.
Giron Jorge MNR SCNDR1: The MRP type.
Wallace Chandler MRS UEAUP1: I think it's. Yep, I think it's MRP type.
Giron Jorge MNR SCNDR1: Correct.
Wilson Susan LVN SCNA12: OK, if you don't want it to run on MRP, what would you put? You still want an inventory number, but what would you put?
Wilson Susan LVN SCNA12: OK.
Simmons Paul MRS UEAUP1: Cindy.
Wilson Susan LVN SCNA12: Thank you.
Wilson Susan LVN SCNA12: So until these guys know what they want, I guess.
Wilson Susan LVN SCNA12: That's what we'll you'll have to put in there unless you want to just say, OK, every time I it runs here, But then I'll order nothing. Right, 'cause. That's what it said.
Wilson Susan LVN SCNA12: Looks like it on here.
Giron Jorge MNR SCNDR1: Alrighty, and it depends how we want to manage it, right?
Giron Jorge MNR SCNDR1: For example, we can also have like a spot buy Po an open PO and just, you know, just grab from that pocket or like you mentioned, we can have a scheduling agreement, but it it it it depends on however you want to handle the system, right?
Wilson Susan LVN SCNA12: And that how to handle the system is inputted from the plant on how they want to reorder cardboard.
Giron Jorge MNR SCNDR1: Correct, correct, correct. At this point of time we have set up for this to be slightly automated and for for the system to make procurement proposals based on the reorder point.
Giron Jorge MNR SCNDR1: This is the way it is set up right now.
Wilson Susan LVN SCNA12: That's. Yeah, that's probably the best way to do it, but we need the input from the guys on what to put in there so.
Simmons Paul MRS UEAUP1: Well, hold on a second now, joray, because what we order our boxes and stuff using.
Simmons Paul MRS UEAUP1: A forecast looking at the past three months and that was one of the things that got missed. Chandler and I were just talking about this.
Simmons Paul MRS UEAUP1: That none of that data got moved over or we are still having problems moving that data over so that anything that is on.
Simmons Paul MRS UEAUP1: A.
Simmons Paul MRS UEAUP1: Bulk and doing.
Simmons Paul MRS UEAUP1: Doing material planning based on looking backwards.
Simmons Paul MRS UEAUP1: Will not be planning correctly at all.
Simmons Paul MRS UEAUP1: We'll know that stuff will be working.
Giron Jorge MNR SCNDR1: And and and that's how you know that's the we're in the perfect timing to catch those little details, those little big details in order to address them.
Giron Jorge MNR SCNDR1: You know, we need to get with Christine and say, hey, we need to have this, you know, time planning time fence or I don't know the safety time. So and so we can make sure that everything is in place.
Giron Jorge MNR SCNDR1: Because I think as of now, we don't have anything or any of those fields filled, right?
Wilson Susan LVN SCNA12: Doesn't look like it.
Wilson Susan LVN SCNA12: Except for the reorder point, that's a start.
Wilson Susan LVN SCNA12: I don't know where they came up with that, but.
Giron Jorge MNR SCNDR1: You see, that's that's important thing of making this like, I don't want to say validation, but just you know this quick like.
Giron Jorge MNR SCNDR1: How can I say setups on how it's going, how it's going, the system going to react or, you know, help us?
Wilson Susan LVN SCNA12: So Paul, you could take these part numbers and run and this is how I've done in the past.
Wilson Susan LVN SCNA12: It's an MB 51 and then you can gauge your usage on that and then put in and devise your men's and Max's that way. And I use men's and Macs loosely there, but.
Wilson Susan LVN SCNA12: Give you an idea of how much you use a month allow.
Wilson Susan LVN SCNA12: A week or two for delivery, and then that'll help you establish.
Wilson Susan LVN SCNA12: Like I said, your men's and Max on how much to order after it gets down to this reorder level, and then you have to account for things like how it's sold, but at least you get a start on this.
Wilson Susan LVN SCNA12: So can if we just give you the cardboard numbers or whatever, can you run an MB 51 on them and?
Wilson Susan LVN SCNA12: Figure out your usage.
McNeely Mary-Kathryn MRS SCNA12: Can run it by vendor code. Yeah, he can run that by vendor code, right?
Wilson Susan LVN SCNA12: No, I mean just in these ones now because that's what's being being tested.
Wilson Susan LVN SCNA12: So you just copy it into MB 51 by part number.
Wilson Susan LVN SCNA12: I'll tell you the vendor code as well. When you do that and in PRD you with me, have you run MB 51 before?
Simmons Paul MRS UEAUP1: Oh, maybe once or twice.
Simmons Paul MRS UEAUP1: Yeah, a lot. A lot.
Wilson Susan LVN SCNA12: You're you're. You're being facetious.
Wilson Susan LVN SCNA12: OK.
Wilson Susan LVN SCNA12: So there you go.
Wilson Susan LVN SCNA12: And then that'll help decide if that's how you want to run, how you control the cardboard inflow from your vend, from the vendors into the plant for outbound cardboard.
Giron Jorge MNR SCNDR1: Red and you know, we can also make some changes during the integration test and see, OK.
Giron Jorge MNR SCNDR1: Let's put this like you mentioned.
Giron Jorge MNR SCNDR1: Let's put this Max.
Giron Jorge MNR SCNDR1: Let's let's put this you know this set up and just to test the system how it is going to react, right? If it is, what are you expecting? Correct. If if if it is what expecting.
Giron Jorge MNR SCNDR1: If it is according to your needs, that's that's. That's the main intent.
Giron Jorge MNR SCNDR1: We can in this stage we can still play around a little bit with information in the system just to see what is more beneficial for us.
Giron Jorge MNR SCNDR1: But yeah, we we we need to start from from something you know and this is what we got as of now.
Simmons Paul MRS UEAUP1: OK.
Giron Jorge MNR SCNDR1: Probably Renee.
Giron Jorge MNR SCNDR1: You can share the list with with the team.
Giron Jorge MNR SCNDR1: And and they can do some comparisons and then they can share, hey, we need to have this, you know, added.
Giron Jorge MNR SCNDR1: Or we don't need this. You know we can.
Giron Jorge MNR SCNDR1: We gonna start from there.
Westrup Renee MNR SCNDR1: Yes. Do I.
Westrup Renee MNR SCNDR1: Do you want me to send you the one with this blue columns?
Westrup Renee MNR SCNDR1: Hidden OK.
Simmons Paul MRS UEAUP1: On head. Yes, please.
Giron Jorge MNR SCNDR1: You know, Tim, what we're trying to do here is.
Giron Jorge MNR SCNDR1: It's it's it's. How can I say the word logs are coming?
Giron Jorge MNR SCNDR1: They are crazy.
Giron Jorge MNR SCNDR1: So we are trying to also like not left you, you know aside and also try to work in parallel mean well we are in the goal life of Fenton and Fowlerville. So we are trying to go one step ahead just that's what we are trying to do here.
Wilson Susan LVN SCNA12: OK.
Giron Jorge MNR SCNDR1: Right, yeah.
Giron Jorge MNR SCNDR1: I think.
Wilson Susan LVN SCNA12: What are we gonna do now? Me and Mary Kay can do them.
Wilson Susan LVN SCNA12: What we can confirm and then Paul, can you do them again?
Wilson Susan LVN SCNA12: Loosely, the men's and Max's.
Wilson Susan LVN SCNA12: You may want to change the reorder point based on your usage of course too.
Simmons Paul MRS UEAUP1: OK.
Giron Jorge MNR SCNDR1: Correct, if you can help us just to check to validate what we currently have is, is it it it matches or makes sense against what you have on PRD and if not just let us know.
Giron Jorge MNR SCNDR1: Hey, we need to change this or we need to add this.
Giron Jorge MNR SCNDR1: Uh, we can start from there.
Giron Jorge MNR SCNDR1: And yeah, for for your validation to send, I think it's simpler right now. But yeah, for example we can check about the material group.
Giron Jorge MNR SCNDR1: Do you want to handle all packaging material into one material group?
Giron Jorge MNR SCNDR1: Honestly, I don't see that incorrectly, but it's going to be up to you.
Giron Jorge MNR SCNDR1: How do you want?
Giron Jorge MNR SCNDR1: Up to which level do you want your reports, right?
Wilson Susan LVN SCNA12: Thank you.
Giron Jorge MNR SCNDR1: OK.
Giron Jorge MNR SCNDR1: It.
Westrup Renee MNR SCNDR1: The the only the other question.
Westrup Renee MNR SCNDR1: Jorge, do you want to meet? Maybe next week before or go live?
Westrup Renee MNR SCNDR1: Because then we will be like driving crazy or.
Giron Jorge MNR SCNDR1: We can this depend on the on the responses, right?
Giron Jorge MNR SCNDR1: If if everything goods looks good, I think you know we can just test it and proceed with the demo. If we need to change criterias on the demo, I think it depends on on on, on the accuracy of the data that.
Giron Jorge MNR SCNDR1: That of the responses that we are going to get from the key users so.
Westrup Renee MNR SCNDR1: So let's wait for.
Giron Jorge MNR SCNDR1: Because if you ask me, I already have book everything.
Giron Jorge MNR SCNDR1: Starting. Yeah. No, I'm starting tomorrow.
Giron Jorge MNR SCNDR1: I have everything booked until October 9th or something like that so but yeah, we still need to to take care of this, so I will say that we just keep working via emails.
Giron Jorge MNR SCNDR1: We just, we don't.
Giron Jorge MNR SCNDR1: We don't need to wait until to to set up a call, you know, and we can.
Giron Jorge MNR SCNDR1: We can start from there.
Giron Jorge MNR SCNDR1: I will say.
Westrup Renee MNR SCNDR1: OK.
Wilson Susan LVN SCNA12: Alright, thank you.
Westrup Renee MNR SCNDR1: Thank you.
Westrup Renee MNR SCNDR1: Have a great day. Thank you.
Giron Jorge MNR SCNDR1: Thank you, Tim.
