from selenium.webdriver.common.by import By
from selenium.common.exceptions import NoSuchElementException


def find_element_in_iframes(driver, by, value):
    iframes = driver.find_elements(By.TAG_NAME, "iframe")

    for idx, iframe in enumerate(iframes):
        try:
            driver.switch_to.frame(iframe)
            element = driver.find_element(by, value)
            print(f"✅ Found element in iframe position {idx}")
            return element
        except NoSuchElementException:
            driver.switch_to.default_content()
            continue

    raise NoSuchElementException(f"❌ Element not found in any iframe: {value}")


def focus_on_last_tab(driver):
    handles = driver.window_handles
    driver.switch_to.window(handles[-1])
