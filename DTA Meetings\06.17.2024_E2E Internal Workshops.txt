<PERSON><PERSON> Matt NRT SCNDR1: The really the sourcing aspect of it, we will go through or not, we let me rephrase that, <PERSON>.
<PERSON><PERSON> Matt NRT SCNDR1: We'll go through the the master relevant master data, show how the info record, and then create the scheduling agreement.
<PERSON><PERSON> Matt NRT SCNDR1: You know, once we get that for.
<PERSON><PERSON> Matt NRT SCNDR1: We'll see the time in and if time permits, then we will go into to creating a or or performing the goods receipt OK.
<PERSON>more Matt NRT SCNDR1: Good.
<PERSON><PERSON> Matt NRT SCNDR1: Yeah.
<PERSON><PERSON> Matt NRT SCNDR1: Let me just take a really quick at the list of the participants that are here.
<PERSON><PERSON> Matt NRT SCNDR1: Alright, so without further ado, who are?
<PERSON><PERSON> Matt NRT SCNDR1: Hey, if you would like to to get started and again guys, this is not uh sorry, sorry, but this is not just a a a dialogue from <PERSON> if any questions anything like that come up please use this opportunity to learn from from each other and really build upon our skill set so we can cover into end processes and in the near future yeah.
<PERSON>more Matt NRT SCNDR1: Alright, alright, thank you.
<PERSON><PERSON> Matt NRT SCNDR1: Your turn.
Giron <PERSON>NR SCNDR1: Cool.
Giron <PERSON> MNR SCNDR1: Alright guys, as <PERSON> already mentioned, I think it's very important for us to understand how or the importance of the material master setup and the vendor master setup for those are the driver points for sourcing right?
<PERSON><PERSON><PERSON> SCNDR1: So in this case what I what I have done is just I downloaded the materials that we have are available on FINS and I just take care of or work.
Giron Jorge MNR SCNDR1: Watch your look for for some specific uh uh master data that we need to have a a in order to perform or to create the scheduled and agreement right?
Giron <PERSON> MNR SCNDR1: In this case, the first, uh, the the the thing that I based on it was on the material master.
Giron Jorge MNR SCNDR1: The material Master has to have a specific configuration and we start with the material type.
Giron Jorge MNR SCNDR1: Let me see if I can share something to you guys.
Giron Jorge MNR SCNDR1: So it is for the material master.
Giron Jorge MNR SCNDR1: It's very important that it has the material type that is the QV one that is the specific for the materials, the productive materials.
Giron Jorge MNR SCNDR1: Let me share going to share one table that we have that is related to the material types in the valuation classes, right?
Giron Jorge MNR SCNDR1: So in this case, we're going to procure third party, umm material procured material.
Giron Jorge MNR SCNDR1: And as you can see, we we need to have a specific configurations, right?
Giron Jorge MNR SCNDR1: We cannot order our create scheduling agreement, for example for an MPM because it's going to be different treatment about the setting of the vom and different things that are going to drive the the purchase of of of of these asset right related to a a component that we are going to sell.
Giron Jorge MNR SCNDR1: Uh, so in this case, it's very important that we notice that we have the CQB one material type and the valuation class that is going to have the the 1000 right, that is the procured from a third party.
Giron Jorge MNR SCNDR1: If we have some something different, as you can see, we have different valuation classes depending on the procured material.
Giron Jorge MNR SCNDR1: We can either have intercompany one that is this one, the 1010, and we can also have that directed buy right.
Giron Jorge MNR SCNDR1: We also have a the classification for semi finished goods and finished goods and the subcontracting piece for each one.
Giron Jorge MNR SCNDR1: OK.
Giron Jorge MNR SCNDR1: Uh, for this exercise we have chosen one that me see.
Giron Jorge MNR SCNDR1: I have it here.
Giron Jorge MNR SCNDR1: We have chosen these material that is a housing uh, what we what?
Giron Jorge MNR SCNDR1: What we look for is on the MRP tab we have a specific configurations that I Christina is not on the call right, but I'm I'm not an expert on the MRP side, but I know that we need to have the MRP controller, MRP Group.
Giron Jorge MNR SCNDR1: So in order to to trigger those tasks right?
Giron Jorge MNR SCNDR1: We. Yeah. Yeah.
Giron Jorge MNR SCNDR1: Sorry. Yeah.
Okal Jan GNL SCNDR1: I don't see the ZQN one material type now in there of is it already available or will it be added?
Okal Jan GNL SCNDR1: It should be for the bailment process.
Okal Jan GNL SCNDR1: Ohh which goes to subcontracting or something like that?
Okal Jan GNL SCNDR1: I'm not sure.
Giron Jorge MNR SCNDR1: That is a a good question, but this is what I have received so far from from finance colleagues.
Giron Jorge MNR SCNDR1: But but I think it would, it would be nice to to ask if we are going to to to have that because actually we couldn't test the bailment component because of the same, right, we we didn't have that component on on the material master, right.
Giron Jorge MNR SCNDR1: But but yeah.
Okal Jan GNL SCNDR1: I just heard today that it we will have ZQN one so OK.
Giron Jorge MNR SCNDR1: They're probably is going to come on the future, but as of now this is the the the latest one that I have and this is on what we have based on the well, the test and yeah, but you are right actually that is on the on one JIRA ticket that that that we have open for Fenton and Fowlerville because as you mentioned we couldn't find any material that has the sequence one we are it's under investigation.
Giron Jorge MNR SCNDR1: If the plan doesn't need it, or if something happened with the demo, uh, but yeah, it's it's it's a good question that one actually.
Giron Jorge MNR SCNDR1: And just just say it's worth to mention that we couldn't test it out for, for Fenton and Fowlerville.
Giron Jorge MNR SCNDR1: So as you can see, it's very important to for us to have the the proper material master configuration in the system.
Giron Jorge MNR SCNDR1: Otherwise, the system is not going to be able to to run as as it should be, right?
Giron Jorge MNR SCNDR1: We also look at the procurement type.
Giron Jorge MNR SCNDR1: F stands for external procurement and in this case the special procurement type.
Giron Jorge MNR SCNDR1: It should be blank because it's a normal one, but if you want some special procurement times, we have the subcontracting and the consignment ones.
Giron Jorge MNR SCNDR1: As you can see, you as you could say that we have the payment component but as of now I haven't seen a material with that material master setup.
Giron Jorge MNR SCNDR1: OK.
Giron Jorge MNR SCNDR1: We also have on the material master we have some configuration regarding the service locations that is that important because it's going to affect directly the inbound piece and most of these uh information is pulled from from the material master.
Giron Jorge MNR SCNDR1: OK, we have a some steps to create this scheduling agreement.
Giron Jorge MNR SCNDR1: First, we need to have a a quality info record created for this material.
Giron Jorge MNR SCNDR1: That was another point of or pain point for us in Fenton and Father regarding the integration test because those quality inforecords weren't migrated, right?
Giron Jorge MNR SCNDR1: So we needed to create those.
Giron Jorge MNR SCNDR1: Whenever we were creating the OR running the test right, I have I have created these like sheet sheet that help us to.
Giron Jorge MNR SCNDR1: How can I say identify the fields that we need to input right?
Giron Jorge MNR SCNDR1: I started with a quality info record.
Giron Jorge MNR SCNDR1: I didn't put the steps because this is more on the quality side.
Giron Jorge MNR SCNDR1: I know how to do it, but he just told me like that generics one.
Giron Jorge MNR SCNDR1: So I put the transaction code.
Giron Jorge MNR SCNDR1: Here is a Qi 062 to perform the quality inforecord right from there we need to create the purchasing info.
Giron Jorge MNR SCNDR1: Record that is on the transaction Emmy, 11 Umm, let me open.
Giron Jorge MNR SCNDR1: I can display it because I I I already create this test so we can just just run it. Yep.
Ashmore Matt NRT SCNDR1: Alright, Jose has a question.
Angeles Jose Erick SNT TSE: Thank you, Matt.
Angeles Jose Erick SNT TSE: So I have a question coming back to your first point about the the but James that we must have in the material master that that we have kind of an Excel because I have been working with several or different systems right in different different reporting units.
Angeles Jose Erick SNT TSE: So not all of them has the same data, but now I just want to make sure, for example that I know or I will be not be able to know which parameters are they the correct ones, or the the mandatory ones in order to identify or proceed with the next step.
Angeles Jose Erick SNT TSE: For example, the scheduling agreement creation and so on, so that we have kind of excel with the the fields or the data that we must check as a mandatory data.
Giron Jorge MNR SCNDR1: Actually, that's a good question and I will actually propose a Matt that if we can create that, that table is going to be helpful for for all of us.
Giron Jorge MNR SCNDR1: And for example, I know I have been working with German.
Giron Jorge MNR SCNDR1: He provided me like some specific for inbound conditions.
Giron Jorge MNR SCNDR1: Itself.
Giron Jorge MNR SCNDR1: Now as you mentioned, yeah, I feel like I feel your question because I have been living that pain also because ASAP is is customized right and and like you mentioned in every project or company they use it in a different way, right.
Giron Jorge MNR SCNDR1: Uh.
Giron Jorge MNR SCNDR1: However, I don't I I haven't seen one master file like that, but I will ask the the I will.
Giron Jorge MNR SCNDR1: See if the team has seen something like that, or if they are.
Giron Jorge MNR SCNDR1: Also agree that we need to to to create that based on the everyone's input.
Angeles Jose Erick SNT TSE: Thank you.
Giron Jorge MNR SCNDR1: I don't know if it if it exists or somebody can compliment on that.
Ashmore Matt NRT SCNDR1: I would say it probably doesn't exist, but, but let's check with Jan Keller when we have an opportunity and if one doesn't exist, then maybe we can work together to to create this, yeah.
Okal Jan GNL SCNDR1: And and I think also another thing, many of the things are not necessarily mandatory or a must in a material master, but if they are not populated then when you create a scattering grain, you will have to fill it manually in the scheduling agreement.
Okal Jan GNL SCNDR1: So you know, if you have them here, it will be taken over if you don't have them, you will have to put it in a scheduling agreement itself.
Giron Jorge MNR SCNDR1: And I sorry, no no Google Google.
Ashmore Matt NRT SCNDR1: Yeah, we should consider that when we create one, you know, let's call it mandatory preferred and and and we can we can create it custom if there isn't something already, yeah.
Giron Jorge MNR SCNDR1: Yeah, I can share this.
Giron Jorge MNR SCNDR1: Sheet that I have created and in here you have the mandatory fields that needs to be populated for each task.
Giron Jorge MNR SCNDR1: As you can see here I have put for example for the info records you need the material that supplier purchasing org and plant in order to proceed.
Giron Jorge MNR SCNDR1: Then it's going to ask you for the purchasing group, the valuation tag that should come from the material master.
Giron Jorge MNR SCNDR1: This is optional to put the no ERS component depending on on how is the, how can I set up the the process with the with the specific supplier, then the tax code and and and you have the the the fields here that you need to put.
Giron Jorge MNR SCNDR1: Some of them are mandatory, for example for inbound the confirmation control key is always going to be 00, four or three zeros 4 and that that that is a yeah, a another good example.
Giron Jorge MNR SCNDR1: But As for the material master setup, I think it will be good or nice to have a generic or master file and also it's going to be helpful for creating the demos right?
Giron Jorge MNR SCNDR1: But OK, let's proceed.
Giron Jorge MNR SCNDR1: With the Inforecord is very important because it's one of the most important master data in sourcing, because in here you are going to set up the relationship with the specific material and the supplier.
Giron Jorge MNR SCNDR1: But not only that, also the price that is very important for us instead of this is a way of to control changes on the pricing and that is this specific setup is going to be transferred into the scheduling agreement also.
Giron Jorge MNR SCNDR1: So in this case, let me open just for you to see the.
Giron Jorge MNR SCNDR1: Inforecord that we have created for this example.
Giron Jorge MNR SCNDR1: You can display the infrared group here.
Giron Jorge MNR SCNDR1: You just put the material supplier plant in partition organization.
Giron Jorge MNR SCNDR1: You you can also have a a inforecords for the special procurement types like so contracting them and consignment.
Giron Jorge MNR SCNDR1: Right.
Giron Jorge MNR SCNDR1: And here you have some supplier data.
Giron Jorge MNR SCNDR1: Umm, these are generics.
Giron Jorge MNR SCNDR1: We usually don't move this section and we go to the purchasing our data that here is where we put our inputs, right.
Giron Jorge MNR SCNDR1: You can put the delivery plan delivery time.
Giron Jorge MNR SCNDR1: As of now, I haven't seen that this is changed, so it's always one uh.
Giron Jorge MNR SCNDR1: You put the purchasing group.
Giron Jorge MNR SCNDR1: Uh.
Giron Jorge MNR SCNDR1: Then you go to the acknowledge required.
Giron Jorge MNR SCNDR1: This is mandatory.
Giron Jorge MNR SCNDR1: You have to check this box.
Giron Jorge MNR SCNDR1: As mentioned, you have for scheduling agreements the confirmation control key is going to be always 004 and you have the the future of New Year is component.
Giron Jorge MNR SCNDR1: In this case, we, we we left it blank for for to run to run the test, because otherwise you're going to need the supplier to have that.
Giron Jorge MNR SCNDR1: How can I say communication with the supplier right?
Giron Jorge MNR SCNDR1: Then we have the net price and we have the the Valley too.
Giron Jorge MNR SCNDR1: In this case I put a generic date, however it is mandatory that is, it should be a realistic date right?
Giron Jorge MNR SCNDR1: According to the negotiator or the contracted terms with the supplier, if you go to conditions, we can specify certain conditions regarding the price and another extra components regarding the as a phone example, it could be the fried uh that at the end of the day it could be a.
Giron Jorge MNR SCNDR1: It's going to be a lump sum with the cross prize fried price and it's going to be taken in consideration for for whenever we are going to.
Giron Jorge MNR SCNDR1: Ohh.
Giron Jorge MNR SCNDR1: Requests or pay the service.
Giron Jorge MNR SCNDR1: Right, yes.
Okal Jan GNL SCNDR1: But if I can have a question, the per in the pricing, is there some standard because today we had discussion during the demo files and we were discussing pricing per 100 and you know if this is correct then yeah, the pricing should be per 100 and just mixing it between one and 100.
Okal Jan GNL SCNDR1: It usually in practice creates a issues because sooner or later somebody screws it up and it creates huge issues.
Giron Jorge MNR SCNDR1: Jenna as my way of seeing this is depending on how the supplier is going to render or deliver the the materials.
Giron Jorge MNR SCNDR1: Right.
Giron Jorge MNR SCNDR1: Uh, I see that I have that they have that component.
Giron Jorge MNR SCNDR1: Uh for the 100 piece?
Giron Jorge MNR SCNDR1: When it's when it has to be divided by 100, I I don't know how to explain it like right away, but it's just regarding the costing of the of the product, right?
Giron Jorge MNR SCNDR1: In this case, we we we have very explicit that is one is going to be 50 per piece, right?
Okal Jan GNL SCNDR1: Yeah, I I understand.
Okal Jan GNL SCNDR1: Just today, somebody from accounting mentioned that the pricing shall be per 100.
Okal Jan GNL SCNDR1: As a standard.
Okal Jan GNL SCNDR1: That's why I'm asking.
Okal Jan GNL SCNDR1: I I don't care personally, but.
Giron Jorge MNR SCNDR1: You know that that we just buy it, right?
Giron Jorge MNR SCNDR1: But but yeah.
Giron Jorge MNR SCNDR1: I think at the end of the day is how is the.
Giron Jorge MNR SCNDR1: Contracted or the negotiated conditions with with the superior?
Giron Jorge MNR SCNDR1: Umm but yeah, I think it's it's it's it's it could be a I'll talk. Good question.
Giron Jorge MNR SCNDR1: I'm I'm going to write down the the question that I cannot answer in order to touch base with a with the experts in in order to get some clarification in there.
Giron Jorge MNR SCNDR1: Uh, any other questions regarding the inforecord?
Giron Jorge MNR SCNDR1: So now whenever we have our infrared recreated, umm, we can proceed on creating the scheduling agreement, right?
Giron Jorge MNR SCNDR1: To create the scheduling agreement is going to be on the transaction code, Amit, 31 L and we have these fields that are mandatory right?
Giron Jorge MNR SCNDR1: For example, the agreement type is going to be always LPA for scheduling agreements.
Giron Jorge MNR SCNDR1: This is how we Identity that it's actually on scheduling agreement.
Giron Jorge MNR SCNDR1: We are going to be asked for the supplier, the validity start and end date.
Giron Jorge MNR SCNDR1: As mentioned prior, they should be realistic date according to the negotiated terms.
Giron Jorge MNR SCNDR1: The language is going to be transported from the vendor master.
Giron Jorge MNR SCNDR1: We're going to be asked for the purchasing group, purchasing organization company code and the payment terms and income terms are going to be also transferred from from the vendor master.
Giron Jorge MNR SCNDR1: Then we need to go to the item tabs.
Giron Jorge MNR SCNDR1: Uh, here, we put the items as specifics.
Giron Jorge MNR SCNDR1: Uh, the item category is going to be well in this case, it's going to be a standard if we have subcontracted or consignment, we need to to change this to to accordingly it's going to be it's going to ask also the material again the plant whenever we put the material in the plant used to you just click enter and the price storage location is going to be transferred from from the purchasing info record and we just need to put the target quantity.
Giron Jorge MNR SCNDR1: The target quantity is going to be.
Giron Jorge MNR SCNDR1: How many pieces are we going to or are we expect to receive from the supplier based on the?
Giron Jorge MNR SCNDR1: Dates that we have uh negotiated for the for the life of the contract that then we need to go to the handling tab and we need to put the confirmation control key again.
Giron Jorge MNR SCNDR1: It's going to be 004 and then the creation profile on the output control tab that is going to be 0099.
Giron Jorge MNR SCNDR1: As far as I have seen these two.
Giron Jorge MNR SCNDR1: To uh, components are are, are are fixed values and they should be like this.
Giron Jorge MNR SCNDR1: Now let me I'm going to open this scheduling agreement for you for us to to see it this information, but just in this catering agreement.
Giron Jorge MNR SCNDR1: Went to to the header first.
Giron Jorge MNR SCNDR1: Here we have the first the first information that we input, we have the purchasing organization purchasing group, the agreement type, the company code and the dates.
Giron Jorge MNR SCNDR1: As mentioned, the language is auto populated and along with that terms of payment you're gonna see here we have set up for these vendor master's going to be paid on 60 days and we have the incoterm as well, uh.
Giron Jorge MNR SCNDR1: Whenever we are creating a scheduling agreement, we need to check these fields because they are transferred, but it's a a good practice to always check if that correct values were transferred.
Giron Jorge MNR SCNDR1: Now here we can see the item details.
Giron Jorge MNR SCNDR1: We have the material as mentioned, we put the material in the, the rest is going to be auto populated, but we need to put the quantity.
Giron Jorge MNR SCNDR1: In this case I just put a generic one just to for for for the test, right.
Giron Jorge MNR SCNDR1: However, from the for the info records in very important because without the inforecord you cannot create the scheduling agreement.
Giron Jorge MNR SCNDR1: Umm, so you have also the storage location that is going to be transferred from what we input on the.
Giron Jorge MNR SCNDR1: Purchasing info record and the material group.
Giron Jorge MNR SCNDR1: Cool.
Giron Jorge MNR SCNDR1: Uh, questions regarding the scheduling agreement?
Angeles Jose Erick SNT TSE: I have a question here about the price.
Angeles Jose Erick SNT TSE: This will be taken from the INFORECORD or will be put directly in the item conditions part in the scheduling agreement level at the scheduling agreement level.
Angeles Jose Erick SNT TSE: How will be handle?
Giron Jorge MNR SCNDR1: Like mentioned is.
Giron Jorge MNR SCNDR1: Yeah, it's for us like to control the pricing, the changes and so and so on.
Angeles Jose Erick SNT TSE: Umm, OK, so the conditions will be created purchasing for record level, OK.
Okal Jan GNL SCNDR1: And and will you have the setting word one updates with another with somebody changes the infrarecorder changes the scheduling agreement or the other way around there is somewhere.
Okal Jan GNL SCNDR1: Check Mark in the scheduling agreement.
Okal Jan GNL SCNDR1: Does that, at least in LP one?
Giron Jorge MNR SCNDR1: Yeah, it's.
Giron Jorge MNR SCNDR1: It should happen.
Giron Jorge MNR SCNDR1: Yep.
Giron Jorge MNR SCNDR1: But umm, do you remember?
Giron Jorge MNR SCNDR1: Where?
Giron Jorge MNR SCNDR1: What is that feel?
Giron Jorge MNR SCNDR1: Just to check it, we can check it here.
Caballero Aldana Paula AHL SCNDR1: I.
Okal Jan GNL SCNDR1: Ohh, I think it was on a I think it was an item level fire.
Caballero Aldana Paula AHL SCNDR1: You can select the item line and go to the bottom with the no with the 100 and the dollar sign.
Giron Jorge MNR SCNDR1: Mm-hmm.
Caballero Aldana Paula AHL SCNDR1: And you know, it's different.
Caballero Aldana Paula AHL SCNDR1: So usually well on the sales side, you have like a button that says update, so it will show you the new price.
Angeles Jose Erick SNT TSE: What you're looking here can you can you?
Giron Jorge MNR SCNDR1: It just the field to set up to.
Giron Jorge MNR SCNDR1: How can I say to update whenever we update the purchasing info record to update the pricing here automatically?
Okal Jan GNL SCNDR1: Yes.
Okal Jan GNL SCNDR1: Or the other way around.
Okal Jan GNL SCNDR1: I'm not sure, it just so they don't get array.
Okal Jan GNL SCNDR1: Yes.
Okal Jan GNL SCNDR1: OK, that's the one.
Giron Jorge MNR SCNDR1: And that is auto populated, yeah.
Okal Jan GNL SCNDR1: Yeah, I think that is very important to have there.
Giron Jorge MNR SCNDR1: Yeah.
Giron Jorge MNR SCNDR1: And this is auto populated.
Okal Jan GNL SCNDR1: OK.
Giron Jorge MNR SCNDR1: And I think this comes from the material master.
Giron Jorge MNR SCNDR1: Honestly, I'm I'm not 100% sure, but a a definitely on the training material that we have on available on the SEV, we don't, we don't, we don't input these manually.
Okal Jan GNL SCNDR1: OK.
Giron Jorge MNR SCNDR1: Cool.
Angeles Jose Erick SNT TSE: And here as per my experience here we should check as well or just put that like as a note there that we must verify those those checkboxes like a good receipt based on the invoice verification and so on because on my experience sometimes they they forgot to activate in the purchasing for record for example and then some when they create this schedule in agreements they did not activate.
Giron Jorge MNR SCNDR1: Yeah.
Giron Jorge MNR SCNDR1: Correct.
Giron Jorge MNR SCNDR1: Then I think, uh, it's not in the training material because I think it's already automated.
Giron Jorge MNR SCNDR1: That's that's my kissing.
Giron Jorge MNR SCNDR1: But yeah, I think I'm agree with you.
Giron Jorge MNR SCNDR1: I think we we need to check it because at the end of the day, if it is not a outdated automatically, it's going to cost a lot of issues.
Angeles Jose Erick SNT TSE: Yeah, sometimes.
Angeles Jose Erick SNT TSE: For example, specifically this GR based on The Voice verification, or if you based on GRI, don't remember correct name.
Angeles Jose Erick SNT TSE: So how comes from the vendor master data?
Angeles Jose Erick SNT TSE: Right, so sometimes it's not activate accordingly there.
Angeles Jose Erick SNT TSE: Then they did not activate neither in the inforecord.
Angeles Jose Erick SNT TSE: So when they create the scheduling agreement, they forgot to activate.
Angeles Jose Erick SNT TSE: So we must have wealth validate this.
Okal Jan GNL SCNDR1: And and especially if it's on a ERS, the vendor, then we keep paying wrong pricing and three, four month later you will find it out and then help like breaks loose.
Angeles Jose Erick SNT TSE: Yes.
Giron Jorge MNR SCNDR1: Right.
Angeles Jose Erick SNT TSE: So just to press and hold here will be very helpful for for all of us.
Giron Jorge MNR SCNDR1: Then I'll when whenever we have created the schedule and women with these mandatory fields, we just need to maintain the source list.
Giron Jorge MNR SCNDR1: The source list is maintained on the T code MMBE 01 and it's pretty simple.
Giron Jorge MNR SCNDR1: It's just going to ask for the material, the plant or again the dates supplier, the agreement that we have, the agreement is the scheduling agreement, the item agreement item, the purchasing organization and the material planning is going to be always too for for this scheduling agreements that is stands for record relevant for MRP schedule lines generate automatically.
Giron Jorge MNR SCNDR1: Hope.
Giron Jorge MNR SCNDR1: Questions regarding sorting E.
Giron Jorge MNR SCNDR1: We can display the the.
Giron Jorge MNR SCNDR1: The source list if you want.
Giron Jorge MNR SCNDR1: We just put the material, the plant and here we go.
Giron Jorge MNR SCNDR1: Ohh, whenever we have a for example that we have the same component but delivered by two different suppliers, we need to maintain the source list prior in order to.
Giron Jorge MNR SCNDR1: Uh let the system to proceed on creating another purchasing document related to that material.
Giron Jorge MNR SCNDR1: For example, we can have the prototypes, we can put for the prototypes without the scheduling agreement, and it's going to be able to.
Giron Jorge MNR SCNDR1: To request for that material right, we can also have the dual source component that for example, we can have the same material but delivered from two different two players with two different scheduling agreements.
Giron Jorge MNR SCNDR1: We can also put that in here, but it's going to request another specific component that is the quota arrangement, OK.
Westrup Renee MNR SCNDR1: For him, I think it's also important to mention the number 2 in the MRP because this enables the MRP to run automatically.
Giron Jorge MNR SCNDR1: Yeah, this is a mandatory field for for the scheduling agreements.
Giron Jorge MNR SCNDR1: Ohh, questions regarding sourcing.
Segura Jose Angel MNR SCNDR1: I I just have a question regarding the scaling agreement, if it's possible to have more than one item.
Segura Jose Angel MNR SCNDR1: For example, what if I need the agreement for the same material number but in a different locations mean for example one item for for RB00 as you have it there in the in the example, or and if you want it in in other location.
Segura Jose Angel MNR SCNDR1: Is that possible?
Giron Jorge MNR SCNDR1: I think that is possible, but I think also the well yeah, the storage location, I don't know.
Giron Jorge MNR SCNDR1: You can change it manually whenever you are creating the immune or moving it to the warehouse.
Giron Jorge MNR SCNDR1: This is this.
Giron Jorge MNR SCNDR1: Is this a question?
Giron Jorge MNR SCNDR1: Because if not, we should be able to put it in here.
Giron Jorge MNR SCNDR1: However, I haven't seen a a scheduling agreement set up like that.
Okal Jan GNL SCNDR1: Maybe.
Okal Jan GNL SCNDR1: Maybe one comment from practice from this.
Okal Jan GNL SCNDR1: As long as you start having more than one line in scheduling agreement, I mean two more than one valid line.
Okal Jan GNL SCNDR1: Uh, it may cause issues with the SS as long as the vendor doesn't supply the linite correct line item, the assets will start failing and they usually don't do it.
Okal Jan GNL SCNDR1: I know it's their fault, but they don't do it correctly.
Okal Jan GNL SCNDR1: If you have only one line, SAP will automatically find out the correct line.
Okal Jan GNL SCNDR1: If you have more than it fails.
Segura Jose Angel MNR SCNDR1: So I think that the best practice will be to create a different agreement for for each location, right?
Okal Jan GNL SCNDR1: Yeah, that would be, I think good practice safer practice.
Angeles Jose Erick SNT TSE: But hose, I have a question here.
Angeles Jose Erick SNT TSE: Why?
Angeles Jose Erick SNT TSE: Why different agreement?
Angeles Jose Erick SNT TSE: State material.
Angeles Jose Erick SNT TSE: Same plan and just different location.
Angeles Jose Erick SNT TSE: Why it's necessary to create?
Angeles Jose Erick SNT TSE: This.
Segura Jose Angel MNR SCNDR1: For example, I mean if if you have a a, an external warehouse and the same material, you need to receive it in in the storage location for the plan and sometimes in in the storage location for the warehouse.
Ashmore Matt NRT SCNDR1: So we have that case and then and fowerville where the the materials can be received in two different locations.
Ashmore Matt NRT SCNDR1: The main plant or the main plant warehouse, which is fowlerville, which would be RD00 and then you have the external warehouse which is EXT O2.
Okal Jan GNL SCNDR1: But then you will have different ship to correct.
Ashmore Matt NRT SCNDR1: Mm-hmm. Yes.
Angeles Jose Erick SNT TSE: That's on different ship to so that, yeah.
Angeles Jose Erick SNT TSE: Just wondering here how this player should not to which schedule agreement against delivery, against which scheduling agreement.
Angeles Jose Erick SNT TSE: Just wondering.
Ashmore Matt NRT SCNDR1: Umm.
Okal Jan GNL SCNDR1: We have the ship to address under wherever you have the truck.
Angeles Jose Erick SNT TSE: So, OK, OK.
Angeles Jose Erick SNT TSE: But supplier must now.
Angeles Jose Erick SNT TSE: So we have, we should have both scaling and agreements with different ship too.
Ashmore Matt NRT SCNDR1: But yeah, he didn't select the ship to here.
Okal Jan GNL SCNDR1: Yeah, but there is something out of a populated.
Okal Jan GNL SCNDR1: So if you would like to ship it somewhere else either.
Okal Jan GNL SCNDR1: And also wherever you have the truck thing, you can set up or it ships to the drop the drop ship location.
Caballero Aldana Paula AHL SCNDR1: If you click on the truck, I think it will show you the delivery address.
Okal Jan GNL SCNDR1: Yeah, you're.
Caballero Aldana Paula AHL SCNDR1: So yeah, it it chooses from location or DCO 0, which must have much 7 address.
Okal Jan GNL SCNDR1: But but you could set up the the drop ship location here.
Angeles Jose Erick SNT TSE: Yep.
Angeles Jose Erick SNT TSE: OK.
Giron Jorge MNR SCNDR1: Hope.
Giron Jorge MNR SCNDR1: Until here is the sourcing piece.
Giron Jorge MNR SCNDR1: From here, it's we we create discovery lines, delivered lines.
Okal Jan GNL SCNDR1: Yeah, it maybe I can have just one practical comment to this specifically for consignment.
Okal Jan GNL SCNDR1: Ohh agreements which we didn't discuss because we don't have an example yet, but I know from practice.
Okal Jan GNL SCNDR1: For consignment, it takes the pricing out of the inforecord and whatever you write in a scheduling agreement per se, it's ignored completely.
Okal Jan GNL SCNDR1: Also, the payment terms, now they're irrelevant in the consignment, it takes it out of the inforecord or payment terms out of the vendor master.
Okal Jan GNL SCNDR1: So somebody needs to make sure they're aligned.
Okal Jan GNL SCNDR1: I have seen so many cases where it was misaligned and it's just a nightmare.
Angeles Jose Erick SNT TSE: Umm.
Giron Jorge MNR SCNDR1: Cool.
Giron Jorge MNR SCNDR1: Thank you.
Giron Jorge MNR SCNDR1: Umm, if there are no further questions regarding searching that will handle it to the Jose you have the your hand raise or it was from the prior.
Giron Jorge MNR SCNDR1: Right now.
Giron Jorge MNR SCNDR1: So yeah, Renee, for you can help me with the info piece.
Giron Jorge MNR SCNDR1: Thank you.
Westrup Renee MNR SCNDR1: OK, I will try my best.
Westrup Renee MNR SCNDR1: So.
Westrup Renee MNR SCNDR1: Can you see my screen?
Westrup Renee MNR SCNDR1: Perfect.
Westrup Renee MNR SCNDR1: So so this is the.
Westrup Renee MNR SCNDR1: So this is you can see the agreement and all the information, but then I go to the ME33L just to figure out, umm, if the information is correct too.
Westrup Renee MNR SCNDR1: For example, part of the validation that I need to make is that the location is correct, the already serious zero and the control figure confirmation control is 0004.
Westrup Renee MNR SCNDR1: So up to here.
Westrup Renee MNR SCNDR1: This is like a a check mark that I need to make.
Westrup Renee MNR SCNDR1: So let me just go to the next step that is adding some demand to the schedule agreement.
Westrup Renee MNR SCNDR1: And as you can see here is the schedule agreement the supplier and now double click on the material.
Westrup Renee MNR SCNDR1: And I already have some demand for the 202223 and I just wanted to show you that the first line is already.
Westrup Renee MNR SCNDR1: Received.
Westrup Renee MNR SCNDR1: So we we receive this information or this 10 pieces, but I added here so how how can you add it that is really easy.
Westrup Renee MNR SCNDR1: You can just put the the date OK and you can put the quantity and you get just do some enters and save for these demand.
Westrup Renee MNR SCNDR1: Do you have any questions until here?
Angeles Jose Erick SNT TSE: Yes, I have a question.
Angeles Jose Erick SNT TSE: When in your previous screen you mentioned that we should have a 004 as a confirmation control, right?
Angeles Jose Erick SNT TSE: So it means that we are expecting to receive ASN.
Angeles Jose Erick SNT TSE: How about this delivery, right?
Westrup Renee MNR SCNDR1: And yes, I I understand.
Angeles Jose Erick SNT TSE: Yes.
Angeles Jose Erick SNT TSE: So what happened?
Angeles Jose Erick SNT TSE: For example, if we do not have uh here the the capability or the suppliers not have the capability to send ASN and we must have received manually the material.
Angeles Jose Erick SNT TSE: So should we keep this 004 just wondering.
Ashmore Matt NRT SCNDR1: You, you you would keep the 004 in the plant would be responsible for creating the ASN manually, yeah.
Angeles Jose Erick SNT TSE: Thank you.
Westrup Renee MNR SCNDR1: So.
Westrup Renee MNR SCNDR1: Do you have any other question until the month?
Westrup Renee MNR SCNDR1: No. So.
Westrup Renee MNR SCNDR1: Uh, now I proceed to make the I'm inbound delivery or the ASN. Sorry.
Westrup Renee MNR SCNDR1: Uh, sorry Sir.
Westrup Renee MNR SCNDR1: And what is what I need to put here is the supplier, the purchase order and make an external ID.
Westrup Renee MNR SCNDR1: So this is like a like a dummy.
Westrup Renee MNR SCNDR1: So I tried to put always late and the timing just to keep on track just made an enter here.
Westrup Renee MNR SCNDR1: Ohh sorry I forgot an important step, so let me go go back.
Westrup Renee MNR SCNDR1: Umm, I'm not an expert of of this, but we need to have the packaging instructions.
Westrup Renee MNR SCNDR1: So we need to create the packaging is do you want me to do the steps of the packaging instructions mat or just to show what I did before?
Ashmore Matt NRT SCNDR1: Yes, you can just show what you you you have already created the packaging instructions.
Ashmore Matt NRT SCNDR1: I think it's OK.
Ashmore Matt NRT SCNDR1: Does everyone understand the packaging instructions?
Ashmore Matt NRT SCNDR1: I mean it's.
Segura Jose Angel MNR SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: If not, then we should have a a further discussion.
Ashmore Matt NRT SCNDR1: I mean an maybe in the more detailed or the more advanced trainings as we progress, we can go into the packaging instructions.
Ashmore Matt NRT SCNDR1: But I think if you just show what you have today, it's OK.
Westrup Renee MNR SCNDR1: OK, so I previously previously made the packaging instructions as a single well so as you can see here is the package or the pallet is selected as a P.
Westrup Renee MNR SCNDR1: So that P is for packaging materials and the M is for the material.
Westrup Renee MNR SCNDR1: So normally you put like the box or the bullet as the first line and the second line is the material, right?
Westrup Renee MNR SCNDR1: So I put like for each pallet or for each patch package I should have 10 pieces of this item, right?
Westrup Renee MNR SCNDR1: Then I need to make a configuration for the conditions, the packaging conditions.
Westrup Renee MNR SCNDR1: And this is a condition that determination type, RCPT and is the.
Westrup Renee MNR SCNDR1: That combination is material plant vendor.
Westrup Renee MNR SCNDR1: So for this is my item.
Westrup Renee MNR SCNDR1: So so I have here that for this material for this supplier and this plant, the packaging instructions that not must be used is the one that I created before.
Westrup Renee MNR SCNDR1: But what happened if we don't have this?
Westrup Renee MNR SCNDR1: We can put another packaging instructions in the second point.
Westrup Renee MNR SCNDR1: The third one is the.
Westrup Renee MNR SCNDR1: Yeah, like an an alternative if we don't have any other or if we have some different kind of packaging instructions.
Westrup Renee MNR SCNDR1: Questions until here.
Westrup Renee MNR SCNDR1: So I assume you're silence is a no, so no. Yeah.
Segura Jose Angel MNR SCNDR1: Yeah, that, that, that we don't have question take.
Westrup Renee MNR SCNDR1: I think you so now we proceed to create the inbound delivery.
Westrup Renee MNR SCNDR1: And as I told you before, uh, we need to put the supplier, the purchase order and external ID that comes from from the supplier.
Westrup Renee MNR SCNDR1: Umm here it gives me the 30 pieces because this is what I miss to deliver right according to my demand.
Westrup Renee MNR SCNDR1: But what happened if I don't deliver these 30 pieces?
Westrup Renee MNR SCNDR1: I need to delete the handling units because they are already created according to the packaging instructions and for example I will delete just one.
Westrup Renee MNR SCNDR1: So I will receive 20 items here.
Westrup Renee MNR SCNDR1: Just make a change on the delivery quantity and one important point is that I need to put the supplier batch so I I just put 1/1 information here as a test and save it.
Westrup Renee MNR SCNDR1: So this is my inbound delivery.
Westrup Renee MNR SCNDR1: So so the next step is to make the good receipt.
Westrup Renee MNR SCNDR1: But.
Westrup Renee MNR SCNDR1: I I know how how to do it in Fiori.
Westrup Renee MNR SCNDR1: And I'm not so sure in going, but let me just teach you how I know to do it and maybe someone else can help me with the other part.
Westrup Renee MNR SCNDR1: And you know, Fury is really fast.
Westrup Renee MNR SCNDR1: So I need a little bit of patience here.
Westrup Renee MNR SCNDR1: So that the app is GR COCKPIT.
Westrup Renee MNR SCNDR1: So so this is the app.
Westrup Renee MNR SCNDR1: You need to put the plan.
Westrup Renee MNR SCNDR1: And you can put either the material, the supplier or the inbound delivery.
Westrup Renee MNR SCNDR1: I will put the inbound delivery.
Westrup Renee MNR SCNDR1: And execute.
Westrup Renee MNR SCNDR1: So here you go find a several pieces of information, for example the the purchase document.
Westrup Renee MNR SCNDR1: That delivery knows the amount the badge, umm, and so on.
Westrup Renee MNR SCNDR1: So to make that good receipt you need to put item OK and just put pass good receipt.
Westrup Renee MNR SCNDR1: So if it's green, it is already received and we just need to save it.
Westrup Renee MNR SCNDR1: And we can get the material document if we need it.
Westrup Renee MNR SCNDR1: I don't know if someone has questions.
Okal Jan GNL SCNDR1: So this is fully integrated with the EWM, the GR cockpit.
Okal Jan GNL SCNDR1: OK. Perfect.
Okal Jan GNL SCNDR1: Perfect.
Okal Jan GNL SCNDR1: Thank you.
Araujo Thiago GCR ENGCL: So, uh, just one more question, Matt.
Araujo Thiago GCR ENGCL: So from my understanding, so that packaging that she created the packaging instruction that she put the quantity for each packaging, we will also be considered in the EWM that same quantity per package.
Ashmore Matt NRT SCNDR1: If if you've seen before, when when she was in the ASN, it had already created, so when she created the ASN it referred to the packaging instructions and created the House already and so it uses that in.
Ashmore Matt NRT SCNDR1: The creation of the of the ASN.
Ashmore Matt NRT SCNDR1: Yeah, there's two options here.
Ashmore Matt NRT SCNDR1: The you.
Ashmore Matt NRT SCNDR1: And I don't remember.
Ashmore Matt NRT SCNDR1: Maybe humado.
Ashmore Matt NRT SCNDR1: Are you here?
Ashmore Matt NRT SCNDR1: Yeah, I don't know which is stronger.
Ashmore Matt NRT SCNDR1: If the supplier provides, so if the ASN was provided by the supplier and they provide packaging instructions within the ASN, I think it's stronger than the packaging instructions that we have in SAP and it would use those to create the Hu.
Ashmore Matt NRT SCNDR1: If not, then it it utilizes the packaging instructions in order to predetermine the Hu H US and and and the quantity per Hu, yeah.
Araujo Thiago GCR ENGCL: OK, let's question.
Araujo Thiago GCR ENGCL: Let's say here in Gray court.
Araujo Thiago GCR ENGCL: OK Sir.
Filho Romualdo SRC SCNDR1: Yeah, yeah, yeah.
Filho Romualdo SRC SCNDR1: Is that correct?
Filho Romualdo SRC SCNDR1: But hearing in Brazil, we we are creating the issues, the packaging instructions.
Ashmore Matt NRT SCNDR1: Yeah, because the supplier doesn't send the packaging instruction.
Ashmore Matt NRT SCNDR1: Yeah, I think this is the case in almost every rollout we have right now.
Ashmore Matt NRT SCNDR1: But yeah, I I just wanted to make sure I was right.
Ashmore Matt NRT SCNDR1: Which one is is is the stronger one?
Ashmore Matt NRT SCNDR1: Yeah, I think it's the ASN.
Okal Jan GNL SCNDR1: And and do you know if the vendor provided the Hu numbers would also populate in EWM D external vendor Hu numbers?
Okal Jan GNL SCNDR1: And EWM.
Ashmore Matt NRT SCNDR1: Ah, so really good question.
Ashmore Matt NRT SCNDR1: Umm Renee, can you can we find out that I don't know the answer, to be honest.
Ashmore Matt NRT SCNDR1: OK, when I'm.
Okal Jan GNL SCNDR1: Umm I I don't know how to test it with whatever systems we have available to be honest.
Ashmore Matt NRT SCNDR1: But but maybe I take the question and I asked Philip, I'm not sure it's it's a good question, yeah.
Okal Jan GNL SCNDR1: Yeah, because a specifically, I'm asking because in some of the steps in the EWM, when you continue and this external vendor number is populated, that's the number we are seeing on the user sees on the scanner.
Okal Jan GNL SCNDR1: It's not the internal edge number, but the the the external one.
Ashmore Matt NRT SCNDR1: Yeah, I know.
Ashmore Matt NRT SCNDR1: I know what you mean.
Ashmore Matt NRT SCNDR1: I will ask I I'm not exactly sure.
Ashmore Matt NRT SCNDR1: I don't know how to test it either because we don't have the connection right now, but.
Okal Jan GNL SCNDR1: Umm OK.
Ashmore Matt NRT SCNDR1: Sorry, Tiago.
Ashmore Matt NRT SCNDR1: You had a question?
Araujo Thiago GCR ENGCL: It it was about repacking in cases like we have here in Gray Court as well that maybe the supplier sent us on a package that we know we are going to receive in a packaging from the supplier.
Araujo Thiago GCR ENGCL: But we have to do we have to repack it internally.
Araujo Thiago GCR ENGCL: Uh, then how it's going to work in the system?
Araujo Thiago GCR ENGCL: Basically, as understood, the ASN we will have the quantity that the supplier is is sending to us and then they choose will be created in the system right?
Araujo Thiago GCR ENGCL: And then we have to repack them internally for some reason.
Ashmore Matt NRT SCNDR1: We have a repack process in the warehouse EWM string.
Araujo Thiago GCR ENGCL: And then change all the quantities and they choose.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And here, here, we we can talk about that.
Ashmore Matt NRT SCNDR1: I maybe I'm Jose and we can we can work on that for the next meeting on Wednesday where we talk about actually what what I would prefer is to to follow the.
Ashmore Matt NRT SCNDR1: Let's say the more basic approach first, where we just confirm the warehouse task.
Ashmore Matt NRT SCNDR1: I know that some of you may know how to release the quality inspection, but we can go through all those steps, warehouse it and then issue it to the line and then we can go into more and in-depth discussions about repack options because we have a lot of those.
Ashmore Matt NRT SCNDR1: OK, it depends on how you want to handle the repack.
Ashmore Matt NRT SCNDR1: You can have multi level Hu.
Ashmore Matt NRT SCNDR1: There's a lot to be discussed there.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: And then maybe for the last minute, umm the the GR was posted.
Ashmore Matt NRT SCNDR1: Can we look at the warehouse monitor, Jose, for this part and and see that there's an at least an open warehouse task?
Segura Jose Angel MNR SCNDR1: I just.
Segura Jose Angel MNR SCNDR1: Did you want me to to do it or or you go do?
Segura Jose Angel MNR SCNDR1: Sorry, did I I came in in, in my car.
Segura Jose Angel MNR SCNDR1: I went for for my kids, so yeah, sorry.
Segura Jose Angel MNR SCNDR1: Sorry, could you do it please?
Segura Jose Angel MNR SCNDR1: Rene and I, I just almost gone in, in, in a place. Sorry.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: If not, I can.
Ashmore Matt NRT SCNDR1: Renee, it's not a problem.
Westrup Renee MNR SCNDR1: The the only thing is I hate this stupid thing from teams that pops up.
Westrup Renee MNR SCNDR1: Ohh hear you.
Westrup Renee MNR SCNDR1: So I think it's somewhere like this and then.
Westrup Renee MNR SCNDR1: And.
Westrup Renee MNR SCNDR1: Equipment. Materials.
Ashmore Matt NRT SCNDR1: If you look under SCM Extended warehouse management then I think it's under extended warehouse management then monitoring.
Ashmore Matt NRT SCNDR1: Yeah, yeah.
Westrup Renee MNR SCNDR1: And here, right.
Ashmore Matt NRT SCNDR1: You you can go to just documents and then look at warehouse task or you can do it this way.
Ashmore Matt NRT SCNDR1: So there's a lot of options.
Ashmore Matt NRT SCNDR1: What Renee is showing, you can also look by the inbound uh.
Ashmore Matt NRT SCNDR1: Delivery the ASN so if you have the ASN you can look there, Renee.
Westrup Renee MNR SCNDR1: OK, so the ASN is this one.
Ashmore Matt NRT SCNDR1: But you got to Scroll down to.
Ashmore Matt NRT SCNDR1: I think it's Ellie and this Scroll down.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: No, it actually is.
Ashmore Matt NRT SCNDR1: It's a little confusing.
Ashmore Matt NRT SCNDR1: Sometimes you will see it's inbound delivery.
Ashmore Matt NRT SCNDR1: Sometimes I.
Okal Jan GNL SCNDR1: I think it is down at the bottom.
Ashmore Matt NRT SCNDR1: I thought it was too, but uh, I thought it would say warehouse.
Ashmore Matt NRT SCNDR1: Can you double click again?
Okal Jan GNL SCNDR1: Curl down.
Ashmore Matt NRT SCNDR1: Scroll down.
Okal Jan GNL SCNDR1: More, more, more.
Ashmore Matt NRT SCNDR1: A lot.
Okal Jan GNL SCNDR1: More.
Okal Jan GNL SCNDR1: There is le delivery, there is it and wipe out the one on you have on the top.
Westrup Renee MNR SCNDR1: Ah yeah, sorry.
Ashmore Matt NRT SCNDR1: Yeah, it's kind of misleading.
Ashmore Matt NRT SCNDR1: Don't use it where it says under the header data inbound delivery.
Ashmore Matt NRT SCNDR1: You should use the LE delivery and and now you see that we have this can you click warehouse task the little?
Ashmore Matt NRT SCNDR1: Yes, please. Uh-huh.
Ashmore Matt NRT SCNDR1: Here you see we have two warehouse tests for the two Hu that she created.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And we'll go into more detail, but if you scroll to the right, you'll see that there is probably the destination.
Ashmore Matt NRT SCNDR1: Bin that they're going to the source that they're coming from.
Ashmore Matt NRT SCNDR1: All all of this information and the status stock type status which like I said we we can go into more detail here next week.
Ashmore Matt NRT SCNDR1: But for the first session, I think it was really good.
Ashmore Matt NRT SCNDR1: I wanna thank Jorge and and Renee.
Ashmore Matt NRT SCNDR1: Great job.
Ashmore Matt NRT SCNDR1: Jorge, if you could share your CHEAT SHEET with everyone, the one you showed in the Excel, that would be great.
Westrup Renee MNR SCNDR1: I have this steps.
Westrup Renee MNR SCNDR1: I don't know if you want.
Westrup Renee MNR SCNDR1: Like the packaging instructions creation and the return to supplier Boy, it helps.
Westrup Renee MNR SCNDR1: Until here I can put it maybe in a word and send it to you.
Ashmore Matt NRT SCNDR1: If I'm, I'm sure for everyone who speaks Spanish, it's great.
Ashmore Matt NRT SCNDR1: That was reading some of this.
Ashmore Matt NRT SCNDR1: OK.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: OK, so if you can, if you can share that, that would be great for next week, Jose or for next week for Wednesday, I think it is, we will go into the warehousing umm confirmation and then we can move into Jan since you were the only one here from the PP stream move into production and then production supply.
Ashmore Matt NRT SCNDR1: But to you, Jose, yeah.
Ashmore Matt NRT SCNDR1: So we will confirm the warehouse task, create some production orders and so on and so forth next week or Wednesday, but one, one housekeeping topic that was brought to my attention Thursday, Friday, don't recall what day it was, but on Friday.
Ashmore Matt NRT SCNDR1: There's a little conflict with the meeting time due to due to the official working hours.
Ashmore Matt NRT SCNDR1: Umm.
Ashmore Matt NRT SCNDR1: Is it possible to change the time for only Fridays meeting to umm one hour earlier or what?
Ashmore Matt NRT SCNDR1: Is everyone's thoughts?
Ochoa Laura MNR UELP: It's for me, Matt.
Ashmore Matt NRT SCNDR1: I know what's your lunch time?
Westrup Renee MNR SCNDR1: It's OK.
Ashmore Matt NRT SCNDR1: I it's or maybe if if.
Ashmore Matt NRT SCNDR1: George Vummadi.
Ashmore Matt NRT SCNDR1: I don't know how it looks 2 hours earlier, so it's not their lunch.
Ashmore Matt NRT SCNDR1: Is this also possible or?
Filho Romualdo SRC SCNDR1: Yes.
Ashmore Matt NRT SCNDR1: OK, so I will change only the the meeting for Friday to two hours earlier, yeah.
Filho Romualdo SRC SCNDR1: Just to let you know, in this next, next Friday we'll be in the general meeting for Materials management.
Filho Romualdo SRC SCNDR1: So we won't be available.
Ashmore Matt NRT SCNDR1: No.
Ashmore Matt NRT SCNDR1: OK. OK.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: Then then we we figure out something to work in in that, that.
Filho Romualdo SRC SCNDR1: Mm-hmm.
Ashmore Matt NRT SCNDR1: Yeah, that meeting.
Ashmore Matt NRT SCNDR1: But OK, thank you for that.
Ashmore Matt NRT SCNDR1: Umm motto.
Ashmore Matt NRT SCNDR1: Yeah, I wanna say thank you, everyone.
Ashmore Matt NRT SCNDR1: Again, I think it was good.
Ashmore Matt NRT SCNDR1: Umm.
Ashmore Matt NRT SCNDR1: And informative.
Ashmore Matt NRT SCNDR1: I know it's the basics in the beginning, but I think we should start in the basics and then move to more advanced.
Ashmore Matt NRT SCNDR1: Yeah.
Ashmore Matt NRT SCNDR1: And and root calls not root, causing not.
Ashmore Matt NRT SCNDR1: Not debugging either, but, but you know, problem solving in the later sessions.
Filho Romualdo SRC SCNDR1: OK, great.
Stevaux Jorge SRC SCNDR1: Yeah, there was awesome.
Ashmore Matt NRT SCNDR1: Well, thanks everyone.
Ashmore Matt NRT SCNDR1: And again, Jose and.
Ashmore Matt NRT SCNDR1: Yeah, we we will focus on warehousing and production production supply on Wednesday.
Ashmore Matt NRT SCNDR1: Thank you guys.
Pereira Carolina de Souza SRC ZSSE1: Thank you.
